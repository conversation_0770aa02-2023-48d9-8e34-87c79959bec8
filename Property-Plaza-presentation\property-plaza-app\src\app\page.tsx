'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import ScrollContainer, { Section } from '@/components/ui/ScrollContainer';
import SlideIndicator from '@/components/ui/SlideIndicator';
import Navigation from '@/components/ui/Navigation';
import Button from '@/components/ui/Button';
import { PillarCard } from '@/components/ui/Card';
import { Heading, Paragraph } from '@/components/ui/Typography';
import { FloatingElements } from '@/components/animations/BackgroundAnimation';

export default function Home() {
  const [currentSlide, setCurrentSlide] = useState(1);
  const totalSlides = 9;

  // Track scroll position to update current slide
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      const windowHeight = window.innerHeight;
      const newSlide = Math.floor(scrollPosition / windowHeight) + 1;
      setCurrentSlide(Math.min(Math.max(newSlide, 1), totalSlides));
    };

    // Throttle scroll events for better performance
    let ticking = false;
    const throttledHandleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', throttledHandleScroll, { passive: true });
    return () => window.removeEventListener('scroll', throttledHandleScroll);
  }, []);

  const handleSlideChange = (slide: number) => {
    setCurrentSlide(slide);
  };

  return (
    <ScrollContainer>
      {/* Navigation */}
      <Navigation
        currentSlide={currentSlide}
        totalSlides={totalSlides}
        onSlideChange={handleSlideChange}
      />

      {/* Slide Indicator */}
      <SlideIndicator currentSlide={currentSlide} totalSlides={totalSlides} />

      {/* 1. Hero Section */}
      <Section id="hero" className="bg-gradient-to-br from-background to-primary/10 relative overflow-hidden">
        <FloatingElements />
        <div className="text-center space-y-8 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.2 }}
          >
            <Heading level={1} animate={false} gradient>
              Empower Property Decisions in Bali
            </Heading>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.4 }}
            className="max-w-3xl mx-auto"
          >
            <Paragraph animate={false} className="text-xl md:text-2xl">
              Transparency. Knowledge. Connection. Empowerment.
            </Paragraph>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.6 }}
          >
            <Button size="lg" className="text-lg">
              Start the Experience
            </Button>
          </motion.div>
        </div>
      </Section>

      {/* 2. Market Problem */}
      <Section id="problem" className="bg-white">
        <div className="text-center space-y-12">
          <Heading level={2}>
            The Market Problem
          </Heading>

          <div className="grid md:grid-cols-3 gap-8 mt-16">
            <PillarCard
              icon="❓"
              title="Buyer Confusion"
              description="Confused by leasehold vs ownership rules and complex legal structures"
              delay={0.1}
            />
            <PillarCard
              icon="🚫"
              title="Limited Access"
              description="Sellers lack access to serious foreign buyers and qualified leads"
              delay={0.2}
            />
            <PillarCard
              icon="⚠️"
              title="Trust Issues"
              description="Low trust in the real estate space due to lack of transparency"
              delay={0.3}
            />
          </div>
        </div>
      </Section>

      {/* 3. Mission & 4 Pillars - Placeholder */}
      <Section id="mission" className="bg-background">
        <div className="text-center">
          <Heading level={2}>Mission & 4 Pillars</Heading>
          <Paragraph className="mt-4">Coming Soon - Interactive pillar cards</Paragraph>
        </div>
      </Section>

      {/* 4. About Property Plaza - Placeholder */}
      <Section id="about" className="bg-white">
        <div className="text-center">
          <Heading level={2}>About Property Plaza</Heading>
          <Paragraph className="mt-4">Coming Soon - Statistics and platform showcase</Paragraph>
        </div>
      </Section>

      {/* 5. Paradise Indonesia Partnership - Placeholder */}
      <Section id="partnership" className="bg-background">
        <div className="text-center">
          <Heading level={2}>Paradise Indonesia Partnership</Heading>
          <Paragraph className="mt-4">Coming Soon - Why this collaboration</Paragraph>
        </div>
      </Section>

      {/* 6. Synergy & Joint Value - Placeholder */}
      <Section id="synergy" className="bg-white">
        <div className="text-center">
          <Heading level={2}>Synergy & Joint Value</Heading>
          <Paragraph className="mt-4">Coming Soon - Logo merge animation</Paragraph>
        </div>
      </Section>

      {/* 7. Pilot Campaign Plan - Placeholder */}
      <Section id="pilot" className="bg-background">
        <div className="text-center">
          <Heading level={2}>Pilot Campaign Plan</Heading>
          <Paragraph className="mt-4">Coming Soon - 4-week strategy details</Paragraph>
        </div>
      </Section>

      {/* 8. Metrics & KPIs - Placeholder */}
      <Section id="metrics" className="bg-white">
        <div className="text-center">
          <Heading level={2}>Metrics & KPIs</Heading>
          <Paragraph className="mt-4">Coming Soon - Tracking dashboard preview</Paragraph>
        </div>
      </Section>

      {/* 9. Final CTA - Placeholder */}
      <Section id="cta" className="bg-gradient-to-br from-primary/10 to-accent/10">
        <div className="text-center">
          <Heading level={2}>Let's Launch the Pilot</Heading>
          <Paragraph className="mt-4">Coming Soon - Call to action and contact</Paragraph>
        </div>
      </Section>
    </ScrollContainer>
  );
}
