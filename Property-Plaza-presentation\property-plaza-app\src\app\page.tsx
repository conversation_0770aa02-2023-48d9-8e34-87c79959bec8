'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import ScrollContainer, { Section } from '@/components/ui/ScrollContainer';
import SlideIndicator from '@/components/ui/SlideIndicator';
import Navigation from '@/components/ui/Navigation';
import Button from '@/components/ui/Button';
import { PillarCard } from '@/components/ui/Card';
import { Heading, Paragraph } from '@/components/ui/Typography';
import { FloatingElements } from '@/components/animations/BackgroundAnimation';
import HeroStats from '@/components/sections/HeroStats';
import PartnershipPreview from '@/components/sections/PartnershipPreview';
import { LogoWithText } from '@/components/ui/Logo';

export default function Home() {
  const [currentSlide, setCurrentSlide] = useState(1);
  const totalSlides = 9;

  // Track scroll position to update current slide
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      const windowHeight = window.innerHeight;
      const newSlide = Math.floor(scrollPosition / windowHeight) + 1;
      setCurrentSlide(Math.min(Math.max(newSlide, 1), totalSlides));
    };

    // Throttle scroll events for better performance
    let ticking = false;
    const throttledHandleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', throttledHandleScroll, { passive: true });
    return () => window.removeEventListener('scroll', throttledHandleScroll);
  }, []);

  const handleSlideChange = (slide: number) => {
    setCurrentSlide(slide);
  };

  return (
    <ScrollContainer>
      {/* Navigation */}
      <Navigation
        currentSlide={currentSlide}
        totalSlides={totalSlides}
        onSlideChange={handleSlideChange}
      />

      {/* Slide Indicator */}
      <SlideIndicator currentSlide={currentSlide} totalSlides={totalSlides} />

      {/* 1. Hero Section */}
      <Section id="hero" className="bg-gradient-to-br from-background via-primary/5 to-accent/10 relative overflow-hidden">
        <FloatingElements />

        {/* Hero Content */}
        <div className="text-center space-y-12 relative z-10">
          {/* Logo */}
          <motion.div
            initial={{ opacity: 0, y: -30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.1 }}
            className="flex justify-center"
          >
            <LogoWithText size="lg" />
          </motion.div>

          {/* Main Title with Staggered Animation */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="space-y-6"
          >
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.6 }}
            >
              <Heading level={1} animate={false} gradient className="leading-tight">
                Empower Property Decisions
              </Heading>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.8 }}
            >
              <Heading level={2} animate={false} className="text-accent font-medium">
                in Bali
              </Heading>
            </motion.div>
          </motion.div>

          {/* 4 Pillars Preview */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 1.0 }}
            className="max-w-4xl mx-auto"
          >
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-8">
              {[
                { icon: "🔍", text: "Transparency" },
                { icon: "🧠", text: "Knowledge" },
                { icon: "🤝", text: "Connection" },
                { icon: "💪", text: "Empowerment" }
              ].map((pillar, index) => (
                <motion.div
                  key={pillar.text}
                  className="flex flex-col items-center space-y-3 p-4 rounded-2xl bg-white/20 backdrop-blur-sm border border-white/30 hover:bg-white/30 transition-all duration-300"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1.2 + index * 0.1 }}
                  whileHover={{
                    y: -5,
                    transition: { type: "spring", stiffness: 300, damping: 20 }
                  }}
                >
                  <motion.div
                    className="text-3xl md:text-4xl"
                    whileHover={{ scale: 1.2, rotate: 5 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                  >
                    {pillar.icon}
                  </motion.div>
                  <span className="text-sm md:text-base font-semibold text-text-default">
                    {pillar.text}
                  </span>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* CTA Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 1.6 }}
            className="space-y-6"
          >
            <Paragraph animate={false} className="text-lg md:text-xl max-w-2xl mx-auto text-text-muted">
              Join Property Plaza and Paradise Indonesia in revolutionizing
              Bali's real estate market through transparency and trust.
            </Paragraph>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                size="lg"
                className="text-lg px-8 py-4"
                onClick={() => {
                  const problemSection = document.getElementById('problem');
                  problemSection?.scrollIntoView({ behavior: 'smooth' });
                }}
              >
                Start the Experience
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="text-lg px-8 py-4"
                onClick={() => {
                  const ctaSection = document.getElementById('cta');
                  ctaSection?.scrollIntoView({ behavior: 'smooth' });
                }}
              >
                Skip to Partnership
              </Button>
            </div>
          </motion.div>

          {/* Property Plaza Stats */}
          <HeroStats />

          {/* Partnership Preview */}
          <PartnershipPreview />

          {/* Scroll Hint */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 3.0 }}
            className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
          >
            <motion.div
              animate={{ y: [0, 10, 0] }}
              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
              className="flex flex-col items-center space-y-2 text-text-muted"
            >
              <span className="text-sm font-medium">Scroll to explore</span>
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
              </svg>
            </motion.div>
          </motion.div>
        </div>
      </Section>

      {/* 2. Market Problem */}
      <Section id="problem" className="bg-white">
        <div className="text-center space-y-12">
          <Heading level={2}>
            The Market Problem
          </Heading>

          <div className="grid md:grid-cols-3 gap-8 mt-16">
            <PillarCard
              icon="❓"
              title="Buyer Confusion"
              description="Confused by leasehold vs ownership rules and complex legal structures"
              delay={0.1}
            />
            <PillarCard
              icon="🚫"
              title="Limited Access"
              description="Sellers lack access to serious foreign buyers and qualified leads"
              delay={0.2}
            />
            <PillarCard
              icon="⚠️"
              title="Trust Issues"
              description="Low trust in the real estate space due to lack of transparency"
              delay={0.3}
            />
          </div>
        </div>
      </Section>

      {/* 3. Mission & 4 Pillars - Placeholder */}
      <Section id="mission" className="bg-background">
        <div className="text-center">
          <Heading level={2}>Mission & 4 Pillars</Heading>
          <Paragraph className="mt-4">Coming Soon - Interactive pillar cards</Paragraph>
        </div>
      </Section>

      {/* 4. About Property Plaza - Placeholder */}
      <Section id="about" className="bg-white">
        <div className="text-center">
          <Heading level={2}>About Property Plaza</Heading>
          <Paragraph className="mt-4">Coming Soon - Statistics and platform showcase</Paragraph>
        </div>
      </Section>

      {/* 5. Paradise Indonesia Partnership - Placeholder */}
      <Section id="partnership" className="bg-background">
        <div className="text-center">
          <Heading level={2}>Paradise Indonesia Partnership</Heading>
          <Paragraph className="mt-4">Coming Soon - Why this collaboration</Paragraph>
        </div>
      </Section>

      {/* 6. Synergy & Joint Value - Placeholder */}
      <Section id="synergy" className="bg-white">
        <div className="text-center">
          <Heading level={2}>Synergy & Joint Value</Heading>
          <Paragraph className="mt-4">Coming Soon - Logo merge animation</Paragraph>
        </div>
      </Section>

      {/* 7. Pilot Campaign Plan - Placeholder */}
      <Section id="pilot" className="bg-background">
        <div className="text-center">
          <Heading level={2}>Pilot Campaign Plan</Heading>
          <Paragraph className="mt-4">Coming Soon - 4-week strategy details</Paragraph>
        </div>
      </Section>

      {/* 8. Metrics & KPIs - Placeholder */}
      <Section id="metrics" className="bg-white">
        <div className="text-center">
          <Heading level={2}>Metrics & KPIs</Heading>
          <Paragraph className="mt-4">Coming Soon - Tracking dashboard preview</Paragraph>
        </div>
      </Section>

      {/* 9. Final CTA - Placeholder */}
      <Section id="cta" className="bg-gradient-to-br from-primary/10 to-accent/10">
        <div className="text-center">
          <Heading level={2}>Let's Launch the Pilot</Heading>
          <Paragraph className="mt-4">Coming Soon - Call to action and contact</Paragraph>
        </div>
      </Section>
    </ScrollContainer>
  );
}
