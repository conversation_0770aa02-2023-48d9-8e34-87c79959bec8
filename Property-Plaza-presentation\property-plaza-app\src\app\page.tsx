'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import ScrollContainer, { Section } from '@/components/ui/ScrollContainer';
import SlideIndicator from '@/components/ui/SlideIndicator';
import Button from '@/components/ui/Button';
import { PillarCard } from '@/components/ui/Card';
import { Heading, Paragraph } from '@/components/ui/Typography';
import { FloatingElements } from '@/components/animations/BackgroundAnimation';

export default function Home() {
  const [currentSlide, setCurrentSlide] = useState(1);
  const totalSlides = 9;

  // Track scroll position to update current slide
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      const windowHeight = window.innerHeight;
      const newSlide = Math.floor(scrollPosition / windowHeight) + 1;
      setCurrentSlide(Math.min(newSlide, totalSlides));
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <ScrollContainer>
      {/* Slide Indicator */}
      <SlideIndicator currentSlide={currentSlide} totalSlides={totalSlides} />

      {/* 1. Hero Section */}
      <Section id="hero" className="bg-gradient-to-br from-background to-primary/10 relative overflow-hidden">
        <FloatingElements />
        <div className="text-center space-y-8 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.2 }}
          >
            <Heading level={1} animate={false} gradient>
              Empower Property Decisions in Bali
            </Heading>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.4 }}
            className="max-w-3xl mx-auto"
          >
            <Paragraph animate={false} className="text-xl md:text-2xl">
              Transparency. Knowledge. Connection. Empowerment.
            </Paragraph>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.6 }}
          >
            <Button size="lg" className="text-lg">
              Start the Experience
            </Button>
          </motion.div>
        </div>
      </Section>

      {/* 2. Market Problem */}
      <Section id="problem" className="bg-white">
        <div className="text-center space-y-12">
          <Heading level={2}>
            The Market Problem
          </Heading>

          <div className="grid md:grid-cols-3 gap-8 mt-16">
            <PillarCard
              icon="❓"
              title="Buyer Confusion"
              description="Confused by leasehold vs ownership rules and complex legal structures"
              delay={0.1}
            />
            <PillarCard
              icon="🚫"
              title="Limited Access"
              description="Sellers lack access to serious foreign buyers and qualified leads"
              delay={0.2}
            />
            <PillarCard
              icon="⚠️"
              title="Trust Issues"
              description="Low trust in the real estate space due to lack of transparency"
              delay={0.3}
            />
          </div>
        </div>
      </Section>

      {/* Placeholder sections for remaining slides */}
      {[3, 4, 5, 6, 7, 8, 9].map((slideNum) => (
        <Section key={slideNum} id={`slide-${slideNum}`} className="bg-background">
          <div className="text-center">
            <h2 className="text-4xl font-bold text-text-default">
              Section {slideNum} - Coming Soon
            </h2>
            <p className="text-text-muted mt-4">
              This section will be developed in the next phase
            </p>
          </div>
        </Section>
      ))}
    </ScrollContainer>
  );
}
