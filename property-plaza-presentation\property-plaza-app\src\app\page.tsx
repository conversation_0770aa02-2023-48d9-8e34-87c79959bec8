'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import ScrollContainer, { Section } from '@/components/ui/ScrollContainer';
import SlideIndicator from '@/components/ui/SlideIndicator';

export default function Home() {
  const [currentSlide, setCurrentSlide] = useState(1);
  const totalSlides = 9;

  // Track scroll position to update current slide
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      const windowHeight = window.innerHeight;
      const newSlide = Math.floor(scrollPosition / windowHeight) + 1;
      setCurrentSlide(Math.min(newSlide, totalSlides));
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <ScrollContainer>
      {/* Slide Indicator */}
      <SlideIndicator currentSlide={currentSlide} totalSlides={totalSlides} />

      {/* 1. Hero Section */}
      <Section id="hero" className="bg-gradient-to-br from-background to-primary/10">
        <div className="text-center space-y-8">
          <motion.h1
            className="text-5xl md:text-7xl font-bold text-text-default"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.2 }}
          >
            Empower Property Decisions in Bali
          </motion.h1>
          <motion.p
            className="text-xl md:text-2xl text-text-muted max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.4 }}
          >
            Transparency. Knowledge. Connection. Empowerment.
          </motion.p>
          <motion.button
            className="bg-primary hover:bg-accent text-white px-8 py-4 rounded-full text-lg font-semibold transition-colors duration-300"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.6 }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Start the Experience
          </motion.button>
        </div>
      </Section>

      {/* 2. Market Problem */}
      <Section id="problem" className="bg-white">
        <div className="text-center space-y-8">
          <h2 className="text-4xl md:text-6xl font-bold text-text-default">
            The Market Problem
          </h2>
          <div className="grid md:grid-cols-3 gap-8 mt-12">
            <div className="space-y-4">
              <div className="w-16 h-16 bg-primary/20 rounded-full mx-auto flex items-center justify-center">
                <span className="text-2xl">❓</span>
              </div>
              <h3 className="text-xl font-semibold">Buyer Confusion</h3>
              <p className="text-text-muted">Confused by leasehold vs ownership rules</p>
            </div>
            <div className="space-y-4">
              <div className="w-16 h-16 bg-primary/20 rounded-full mx-auto flex items-center justify-center">
                <span className="text-2xl">🚫</span>
              </div>
              <h3 className="text-xl font-semibold">Limited Access</h3>
              <p className="text-text-muted">Sellers lack access to serious foreign buyers</p>
            </div>
            <div className="space-y-4">
              <div className="w-16 h-16 bg-primary/20 rounded-full mx-auto flex items-center justify-center">
                <span className="text-2xl">⚠️</span>
              </div>
              <h3 className="text-xl font-semibold">Trust Issues</h3>
              <p className="text-text-muted">Low trust in the real estate space</p>
            </div>
          </div>
        </div>
      </Section>

      {/* Placeholder sections for remaining slides */}
      {[3, 4, 5, 6, 7, 8, 9].map((slideNum) => (
        <Section key={slideNum} id={`slide-${slideNum}`} className="bg-background">
          <div className="text-center">
            <h2 className="text-4xl font-bold text-text-default">
              Section {slideNum} - Coming Soon
            </h2>
            <p className="text-text-muted mt-4">
              This section will be developed in the next phase
            </p>
          </div>
        </Section>
      ))}
    </ScrollContainer>
  );
}
