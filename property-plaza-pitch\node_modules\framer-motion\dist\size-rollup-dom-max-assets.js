import{createContext as t}from"react";const e=(t,e,r)=>r>e?e:r<t?t:r,r={},s=t({});function a(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function o(t){return"string"==typeof t||Array.isArray(t)}const n=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],i=["initial",...n];function c(t){return a(t.animate)||i.some(e=>o(t[e]))}function l(t){return Boolean(c(t)||t.variants)}const f="undefined"!=typeof window,p={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},d={};for(const t in p)d[t]={isEnabled:e=>p[t].some(t=>!!e[t])};function u(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}const m=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],g={value:null,addProjectionMetrics:null};function h(t,e){let s=!1,a=!0;const o={delta:0,timestamp:0,isProcessing:!1},n=()=>s=!0,i=m.reduce((t,r)=>(t[r]=function(t,e){let r=new Set,s=new Set,a=!1,o=!1;const n=new WeakSet;let i={delta:0,timestamp:0,isProcessing:!1},c=0;function l(e){n.has(e)&&(f.schedule(e),t()),c++,e(i)}const f={schedule:(t,e=!1,o=!1)=>{const i=o&&a?r:s;return e&&n.add(t),i.has(t)||i.add(t),t},cancel:t=>{s.delete(t),n.delete(t)},process:t=>{i=t,a?o=!0:(a=!0,[r,s]=[s,r],r.forEach(l),e&&g.value&&g.value.frameloop[e].push(c),c=0,r.clear(),a=!1,o&&(o=!1,f.process(t)))}};return f}(n,e?r:void 0),t),{}),{setup:c,read:l,resolveKeyframes:f,preUpdate:p,update:d,preRender:u,render:h,postRender:y}=i,v=()=>{const n=r.useManualTiming?o.timestamp:performance.now();s=!1,r.useManualTiming||(o.delta=a?1e3/60:Math.max(Math.min(n-o.timestamp,40),1)),o.timestamp=n,o.isProcessing=!0,c.process(o),l.process(o),f.process(o),p.process(o),d.process(o),u.process(o),h.process(o),y.process(o),o.isProcessing=!1,s&&e&&(a=!1,t(v))};return{schedule:m.reduce((e,r)=>{const n=i[r];return e[r]=(e,r=!1,i=!1)=>(s||(s=!0,a=!0,o.isProcessing||t(v)),n.schedule(e,r,i)),e},{}),cancel:t=>{for(let e=0;e<m.length;e++)i[m[e]].cancel(t)},state:o,steps:i}}const y=t=>e=>"string"==typeof e&&e.startsWith(t),v=y("--"),w=y("var(--"),b=t=>!!w(t)&&x.test(t.split("/*")[0].trim()),x=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,k={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},P={...k,transform:t=>e(0,1,t)},S={...k,default:1},B=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),O=B("deg"),R=B("%"),T=B("px"),X=B("vh"),Y=B("vw"),W=(()=>({...R,parse:t=>R.parse(t)/100,transform:t=>R.transform(100*t)}))(),$=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],L=(()=>new Set($))(),V={...k,transform:Math.round},Z={borderWidth:T,borderTopWidth:T,borderRightWidth:T,borderBottomWidth:T,borderLeftWidth:T,borderRadius:T,radius:T,borderTopLeftRadius:T,borderTopRightRadius:T,borderBottomRightRadius:T,borderBottomLeftRadius:T,width:T,maxWidth:T,height:T,maxHeight:T,top:T,right:T,bottom:T,left:T,padding:T,paddingTop:T,paddingRight:T,paddingBottom:T,paddingLeft:T,margin:T,marginTop:T,marginRight:T,marginBottom:T,marginLeft:T,backgroundPositionX:T,backgroundPositionY:T,...{rotate:O,rotateX:O,rotateY:O,rotateZ:O,scale:S,scaleX:S,scaleY:S,scaleZ:S,skew:O,skewX:O,skewY:O,distance:T,translateX:T,translateY:T,translateZ:T,x:T,y:T,z:T,perspective:T,transformPerspective:T,opacity:P,originX:W,originY:W,originZ:T},zIndex:V,fillOpacity:P,strokeOpacity:P,numOctaves:V},I=(t,e)=>e&&"number"==typeof t?e.transform(t):t,{schedule:M,cancel:A}=h(queueMicrotask,!1),C=t=>Boolean(t&&t.getVelocity),E=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),F="data-"+E("framerAppearId"),H=t(null),z=t({}),j={};function D(t){for(const e in t)j[e]=t[e],v(e)&&(j[e].isCSSVariable=!0)}function K(t,{layout:e,layoutId:r}){return L.has(t)||t.startsWith("origin")||(e||void 0!==r)&&(!!j[t]||"opacity"===t)}const U={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},q=$.length;function G(t,e,r){const{style:s,vars:a,transformOrigin:o}=t;let n=!1,i=!1;for(const t in e){const r=e[t];if(L.has(t))n=!0;else if(v(t))a[t]=r;else{const e=I(r,Z[t]);t.startsWith("origin")?(i=!0,o[t]=e):s[t]=e}}if(e.transform||(n||r?s.transform=function(t,e,r){let s="",a=!0;for(let o=0;o<q;o++){const n=$[o],i=t[n];if(void 0===i)continue;let c=!0;if(c="number"==typeof i?i===(n.startsWith("scale")?1:0):0===parseFloat(i),!c||r){const t=I(i,Z[n]);c||(a=!1,s+=`${U[n]||n}(${t}) `),r&&(e[n]=t)}}return s=s.trim(),r?s=r(e,a?"":s):a&&(s="none"),s}(e,t.transform,r):s.transform&&(s.transform="none")),i){const{originX:t="50%",originY:e="50%",originZ:r=0}=o;s.transformOrigin=`${t} ${e} ${r}`}}const J={offset:"stroke-dashoffset",array:"stroke-dasharray"},N={offset:"strokeDashoffset",array:"strokeDasharray"};function Q(t,{attrX:e,attrY:r,attrScale:s,pathLength:a,pathSpacing:o=1,pathOffset:n=0,...i},c,l,f){if(G(t,i,l),c)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:p,style:d}=t;p.transform&&(d.transform=p.transform,delete p.transform),(d.transform||p.transformOrigin)&&(d.transformOrigin=p.transformOrigin??"50% 50%",delete p.transformOrigin),d.transform&&(d.transformBox=f?.transformBox??"fill-box",delete p.transformBox),void 0!==e&&(p.x=e),void 0!==r&&(p.y=r),void 0!==s&&(p.scale=s),void 0!==a&&function(t,e,r=1,s=0,a=!0){t.pathLength=1;const o=a?J:N;t[o.offset]=T.transform(-s);const n=T.transform(e),i=T.transform(r);t[o.array]=`${n} ${i}`}(p,a,o,n,!1)}const _=t=>"string"==typeof t&&"svg"===t.toLowerCase(),tt=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function et(t){return"string"==typeof t&&!t.includes("-")&&!!(tt.indexOf(t)>-1||/[A-Z]/u.test(t))}function rt(t){const e=[{},{}];return t?.values.forEach((t,r)=>{e[0][r]=t.get(),e[1][r]=t.getVelocity()}),e}function st(t,e,r,s){if("function"==typeof e){const[a,o]=rt(s);e=e(void 0!==r?r:t.custom,a,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){const[a,o]=rt(s);e=e(void 0!==r?r:t.custom,a,o)}return e}function at(t){return C(t)?t.get():t}function ot(t,e,r){const{style:s}=t,a={};for(const o in s)(C(s[o])||e.style&&C(e.style[o])||K(o,t)||void 0!==r?.getValue(o)?.liveStyle)&&(a[o]=s[o]);return a}function nt(t,e,r){const s=ot(t,e,r);for(const r in t)if(C(t[r])||C(e[r])){s[-1!==$.indexOf(r)?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r]=t[r]}return s}export{$ as A,O as B,Y as C,X as D,Z as E,L as F,j as G,v as H,D as I,i as J,n as K,s as L,r as M,E as N,H as P,z as S,o as a,u as b,f as c,C as d,K as e,d as f,G as g,Q as h,c as i,_ as j,et as k,l,M as m,a as n,F as o,st as p,nt as q,at as r,ot as s,h as t,k as u,P as v,e as w,R as x,b as y,T as z};
