'use client';

import { motion } from 'framer-motion';
import { ReactNode } from 'react';

interface TypographyProps {
  children: ReactNode;
  className?: string;
  animate?: boolean;
  delay?: number;
}

interface HeadingProps extends TypographyProps {
  level?: 1 | 2 | 3 | 4 | 5 | 6;
  gradient?: boolean;
}

export function Heading({ 
  children, 
  level = 1, 
  className = '', 
  animate = true,
  delay = 0,
  gradient = false 
}: HeadingProps) {
  const Tag = `h${level}` as keyof JSX.IntrinsicElements;
  
  const baseClasses = {
    1: 'text-5xl md:text-7xl font-bold leading-tight',
    2: 'text-4xl md:text-6xl font-bold leading-tight',
    3: 'text-3xl md:text-4xl font-bold leading-snug',
    4: 'text-2xl md:text-3xl font-semibold leading-snug',
    5: 'text-xl md:text-2xl font-semibold leading-normal',
    6: 'text-lg md:text-xl font-semibold leading-normal'
  };

  const gradientClass = gradient 
    ? 'bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent' 
    : 'text-text-default';

  const classes = `${baseClasses[level]} ${gradientClass} ${className}`;

  if (!animate) {
    return <Tag className={classes}>{children}</Tag>;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay, ease: "easeOut" }}
      viewport={{ once: true, amount: 0.3 }}
    >
      <Tag className={classes}>{children}</Tag>
    </motion.div>
  );
}

export function Paragraph({ 
  children, 
  className = '', 
  animate = true,
  delay = 0 
}: TypographyProps) {
  const classes = `text-lg md:text-xl text-text-muted leading-relaxed ${className}`;

  if (!animate) {
    return <p className={classes}>{children}</p>;
  }

  return (
    <motion.p
      className={classes}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay, ease: "easeOut" }}
      viewport={{ once: true, amount: 0.3 }}
    >
      {children}
    </motion.p>
  );
}

export function Quote({ 
  children, 
  author, 
  className = '', 
  animate = true,
  delay = 0 
}: TypographyProps & { author?: string }) {
  if (!animate) {
    return (
      <blockquote className={`text-2xl md:text-3xl font-medium text-text-default italic text-center ${className}`}>
        "{children}"
        {author && (
          <footer className="text-lg text-text-muted mt-4 not-italic">
            — {author}
          </footer>
        )}
      </blockquote>
    );
  }

  return (
    <motion.blockquote
      className={`text-2xl md:text-3xl font-medium text-text-default italic text-center ${className}`}
      initial={{ opacity: 0, scale: 0.9 }}
      whileInView={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.8, delay, ease: "easeOut" }}
      viewport={{ once: true, amount: 0.3 }}
    >
      <motion.span
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.6, delay: delay + 0.2 }}
        viewport={{ once: true }}
      >
        "{children}"
      </motion.span>
      {author && (
        <motion.footer
          className="text-lg text-text-muted mt-4 not-italic"
          initial={{ opacity: 0, y: 10 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: delay + 0.4 }}
          viewport={{ once: true }}
        >
          — {author}
        </motion.footer>
      )}
    </motion.blockquote>
  );
}

export function Badge({ 
  children, 
  variant = 'primary',
  className = '' 
}: { 
  children: ReactNode; 
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  className?: string;
}) {
  const variants = {
    primary: 'bg-primary/10 text-primary border-primary/20',
    secondary: 'bg-accent/10 text-accent border-accent/20',
    success: 'bg-success/10 text-success border-success/20',
    warning: 'bg-warning/10 text-warning border-warning/20',
    error: 'bg-error/10 text-error border-error/20'
  };

  return (
    <span className={`
      inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
      border ${variants[variant]} ${className}
    `.trim().replace(/\s+/g, ' ')}>
      {children}
    </span>
  );
}
