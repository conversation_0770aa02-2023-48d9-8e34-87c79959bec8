# Property Plaza × Paradise Indonesia | Strategic Partnership Presentation

An interactive, scroll-based presentation showcasing the strategic collaboration between Property Plaza and Paradise Indonesia for transparent property decisions in Bali.

## 🚀 Tech Stack

- **Next.js 15.3.5** - React framework with App Router
- **TypeScript** - Type safety and developer experience
- **TailwindCSS v4** - Utility-first CSS framework with custom design system
- **Framer Motion 12.23.0** - Hardware-accelerated animations and scroll effects
- **Turbopack** - Fast development server

## 🎨 Design System

### Brand Colors
- **Primary**: `#b78b4c` (Warm Gold)
- **Accent**: `#8a6b3c` (Deep Bronze)
- **Background**: `#f7f1eb` (Light Sand)
- **Text Default**: `#1e1e1e` (Rich Black)
- **Text Muted**: `#666666` (Warm Gray)

### Typography
- **Headings**: Playfair Display (elegant serif)
- **Body**: Inter (clean sans-serif)

## 🏗️ Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── layout.tsx         # Root layout with fonts and metadata
│   ├── page.tsx           # Main presentation page
│   └── globals.css        # Global styles and design tokens
├── components/
│   ├── ui/                # Reusable UI components
│   │   ├── Button.tsx     # Professional button variants
│   │   ├── Card.tsx       # Card components (Pillar, Stat)
│   │   ├── ScrollContainer.tsx  # Scroll management
│   │   ├── SlideIndicator.tsx   # Progress indicator
│   │   └── Typography.tsx # Heading, Paragraph, Quote components
│   ├── sections/          # Page sections (planned)
│   └── animations/        # Animation components
│       └── BackgroundAnimation.tsx  # Floating elements
```

## 📱 Features

- **Scroll-Snap Navigation**: Full-screen sections with smooth scrolling
- **Slide Indicator**: Shows current section (e.g., "Slide 3 of 9")
- **Responsive Design**: Mobile-first approach
- **Brand Consistency**: Property Plaza design system
- **Hardware-Accelerated Animations**: Smooth Framer Motion effects
- **Professional Components**: Reusable UI library

## 🎯 Presentation Sections

1. **Hero** - "Empower Property Decisions in Bali"
2. **Market Problem** - Buyer confusion, limited access, trust issues
3. **Mission & 4 Pillars** - Transparency, Knowledge, Connection, Empowerment
4. **About Property Plaza** - Statistics and platform showcase
5. **Paradise Indonesia Partnership** - Why this collaboration
6. **Synergy Visualization** - Joint value proposition
7. **Pilot Campaign Plan** - 4-week strategy details
8. **Metrics & KPIs** - Tracking and measurement
9. **Final CTA** - Call to action and contact

## 🚀 Getting Started

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Run development server:**
   ```bash
   npm run dev
   ```

3. **Open in browser:**
   ```
   http://localhost:3000
   ```

## 🛠️ Development Commands

```bash
npm run dev      # Start development server with Turbopack
npm run build    # Build for production
npm run start    # Start production server
npm run lint     # Run ESLint
```

## 📋 Development Progress

- [x] **Project Setup** - Next.js + TypeScript + TailwindCSS + Framer Motion
- [x] **Brand System** - Colors, typography, spacing tokens
- [ ] **Core Layout** - Navigation and scroll system
- [ ] **Hero Section** - Immersive opening with animations
- [ ] **Content Sections** - All 9 presentation sections
- [ ] **Animations** - Scroll-triggered effects
- [ ] **Mobile Optimization** - Responsive design
- [ ] **Performance** - Bundle optimization and testing

## 🎨 Design Principles

- **Professional**: Formal design for business partnerships
- **Immersive**: Full-screen scroll experience
- **Brand-Consistent**: Property Plaza colors and typography
- **Performance-First**: Hardware-accelerated animations
- **Mobile-Optimized**: Touch-friendly interactions

## 📞 Contact

Property Plaza - Strategic Partnership Presentation
Built with Next.js, TailwindCSS, and Framer Motion
