[{"name": "hot-reloader", "duration": 140, "timestamp": 242256819398, "id": 3, "tags": {"version": "15.3.5"}, "startTime": 1751942126006, "traceId": "be2dcb2270b2a19d"}, {"name": "setup-dev-bundler", "duration": 864328, "timestamp": 242256573595, "id": 2, "parentId": 1, "tags": {}, "startTime": 1751942125760, "traceId": "be2dcb2270b2a19d"}, {"name": "run-instrumentation-hook", "duration": 22, "timestamp": 242257535894, "id": 4, "parentId": 1, "tags": {}, "startTime": 1751942126722, "traceId": "be2dcb2270b2a19d"}, {"name": "ensure-page", "duration": 860, "timestamp": 242257574230, "id": 5, "parentId": 3, "tags": {"inputPage": "/socket.io"}, "startTime": 1751942126761, "traceId": "be2dcb2270b2a19d"}, {"name": "ensure-page", "duration": 201, "timestamp": 242257575148, "id": 6, "parentId": 3, "tags": {"inputPage": "/socket.io"}, "startTime": 1751942126762, "traceId": "be2dcb2270b2a19d"}, {"name": "ensure-page", "duration": 549, "timestamp": 242257576186, "id": 7, "parentId": 3, "tags": {"inputPage": "/socket.io"}, "startTime": 1751942126763, "traceId": "be2dcb2270b2a19d"}, {"name": "ensure-page", "duration": 530, "timestamp": 242257577032, "id": 8, "parentId": 3, "tags": {"inputPage": "/socket.io"}, "startTime": 1751942126763, "traceId": "be2dcb2270b2a19d"}, {"name": "start-dev-server", "duration": 1797570, "timestamp": 242255790372, "id": 1, "tags": {"cpus": "16", "platform": "win32", "memory.freeMem": "1560625152", "memory.totalMem": "16556564480", "memory.heapSizeLimit": "8327790592", "memory.rss": "185995264", "memory.heapTotal": "98746368", "memory.heapUsed": "76099896"}, "startTime": 1751942124977, "traceId": "be2dcb2270b2a19d"}, {"name": "compile-path", "duration": 2246157, "timestamp": 242257586671, "id": 11, "tags": {"trigger": "/_not-found/page"}, "startTime": 1751942126773, "traceId": "be2dcb2270b2a19d"}, {"name": "ensure-page", "duration": 2248318, "timestamp": 242257585220, "id": 10, "parentId": 3, "tags": {"inputPage": "/_not-found/page"}, "startTime": 1751942126772, "traceId": "be2dcb2270b2a19d"}]