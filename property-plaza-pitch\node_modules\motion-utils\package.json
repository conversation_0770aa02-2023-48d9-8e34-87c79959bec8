{"name": "motion-utils", "version": "12.19.0", "author": "<PERSON>", "license": "MIT", "repository": "https://github.com/motiondivision/motion", "main": "./dist/cjs/index.js", "types": "./dist/index.d.ts", "module": "./dist/es/index.mjs", "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/cjs/index.js", "import": "./dist/es/index.mjs", "default": "./dist/cjs/index.js"}}, "scripts": {"clean": "rm -rf types dist lib", "build": "yarn clean && tsc -p . && rollup -c", "dev": "concurrently -c blue,red -n tsc,rollup --kill-others \"tsc --watch -p . --preserveWatchOutput\" \"rollup --config --watch --no-watch.clearScreen\"", "test": "jest --config jest.config.json --max-workers=2"}, "gitHead": "009e4c4e793ecacaebe7066b969c640f0c09f573"}