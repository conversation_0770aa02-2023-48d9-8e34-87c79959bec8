{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/Property-Plaza-presentation/property-plaza-app/src/components/ui/ScrollContainer.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { ReactNode } from 'react';\n\ninterface ScrollContainerProps {\n  children: ReactNode;\n  className?: string;\n}\n\nexport default function ScrollContainer({ \n  children, \n  className = '' \n}: ScrollContainerProps) {\n  return (\n    <motion.div \n      className={`scroll-smooth ${className}`}\n      initial={{ opacity: 0 }}\n      animate={{ opacity: 1 }}\n      transition={{ duration: 0.5 }}\n    >\n      {children}\n    </motion.div>\n  );\n}\n\ninterface SectionProps {\n  children: ReactNode;\n  className?: string;\n  id?: string;\n}\n\nexport function Section({ \n  children, \n  className = '', \n  id \n}: SectionProps) {\n  return (\n    <motion.section\n      id={id}\n      className={`section-snap flex flex-col justify-center items-center px-4 md:px-8 lg:px-16 ${className}`}\n      initial={{ opacity: 0, y: 50 }}\n      whileInView={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.8, ease: \"easeOut\" }}\n      viewport={{ once: true, amount: 0.3 }}\n    >\n      <div className=\"max-w-7xl w-full\">\n        {children}\n      </div>\n    </motion.section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAUe,SAAS,gBAAgB,EACtC,QAAQ,EACR,YAAY,EAAE,EACO;IACrB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,cAAc,EAAE,WAAW;QACvC,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,YAAY;YAAE,UAAU;QAAI;kBAE3B;;;;;;AAGP;KAdwB;AAsBjB,SAAS,QAAQ,EACtB,QAAQ,EACR,YAAY,EAAE,EACd,EAAE,EACW;IACb,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,OAAO;QACb,IAAI;QACJ,WAAW,CAAC,6EAA6E,EAAE,WAAW;QACtG,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC7C,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAI;kBAEpC,cAAA,6LAAC;YAAI,WAAU;sBACZ;;;;;;;;;;;AAIT;MAnBgB", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/Property-Plaza-presentation/property-plaza-app/src/components/ui/SlideIndicator.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\n\ninterface SlideIndicatorProps {\n  currentSlide: number;\n  totalSlides: number;\n  className?: string;\n}\n\nexport default function SlideIndicator({ \n  currentSlide, \n  totalSlides, \n  className = '' \n}: SlideIndicatorProps) {\n  return (\n    <motion.div \n      className={`fixed bottom-8 right-8 z-50 bg-white/10 backdrop-blur-md rounded-full px-4 py-2 ${className}`}\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ delay: 1 }}\n    >\n      <div className=\"flex items-center space-x-2\">\n        <span className=\"text-sm font-medium text-text-default\">\n          Slide {currentSlide} of {totalSlides}\n        </span>\n        <div className=\"flex space-x-1\">\n          {Array.from({ length: totalSlides }, (_, i) => (\n            <motion.div\n              key={i}\n              className={`w-2 h-2 rounded-full transition-colors duration-300 ${\n                i + 1 === currentSlide \n                  ? 'bg-primary' \n                  : 'bg-text-muted/30'\n              }`}\n              whileHover={{ scale: 1.2 }}\n            />\n          ))}\n        </div>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAUe,SAAS,eAAe,EACrC,YAAY,EACZ,WAAW,EACX,YAAY,EAAE,EACM;IACpB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,gFAAgF,EAAE,WAAW;QACzG,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,OAAO;QAAE;kBAEvB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAK,WAAU;;wBAAwC;wBAC/C;wBAAa;wBAAK;;;;;;;8BAE3B,6LAAC;oBAAI,WAAU;8BACZ,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAY,GAAG,CAAC,GAAG,kBACvC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAW,CAAC,oDAAoD,EAC9D,IAAI,MAAM,eACN,eACA,oBACJ;4BACF,YAAY;gCAAE,OAAO;4BAAI;2BANpB;;;;;;;;;;;;;;;;;;;;;AAanB;KAhCwB", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/Property-Plaza-presentation/property-plaza-app/src/components/ui/Button.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { ReactNode } from 'react';\n\ninterface ButtonProps {\n  children: ReactNode;\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';\n  size?: 'sm' | 'md' | 'lg' | 'xl';\n  className?: string;\n  onClick?: () => void;\n  disabled?: boolean;\n  href?: string;\n  target?: string;\n  rel?: string;\n}\n\nconst buttonVariants = {\n  primary: 'bg-primary hover:bg-primary-dark text-white shadow-md hover:shadow-lg',\n  secondary: 'bg-accent hover:bg-accent-dark text-white shadow-md hover:shadow-lg',\n  outline: 'border-2 border-primary text-primary hover:bg-primary hover:text-white',\n  ghost: 'text-primary hover:bg-primary/10'\n};\n\nconst buttonSizes = {\n  sm: 'px-4 py-2 text-sm',\n  md: 'px-6 py-3 text-base',\n  lg: 'px-8 py-4 text-lg',\n  xl: 'px-10 py-5 text-xl'\n};\n\nexport default function Button({\n  children,\n  variant = 'primary',\n  size = 'md',\n  className = '',\n  onClick,\n  disabled = false,\n  href,\n  target,\n  rel\n}: ButtonProps) {\n  const baseClasses = `\n    inline-flex items-center justify-center\n    font-semibold rounded-full\n    transition-all duration-300 ease-out\n    focus:outline-none focus:ring-4 focus:ring-primary/20\n    disabled:opacity-50 disabled:cursor-not-allowed\n    ${buttonVariants[variant]}\n    ${buttonSizes[size]}\n    ${className}\n  `.trim().replace(/\\s+/g, ' ');\n\n  const MotionComponent = motion.button;\n\n  if (href) {\n    return (\n      <motion.a\n        href={href}\n        target={target}\n        rel={rel}\n        className={baseClasses}\n        whileHover={{ scale: disabled ? 1 : 1.05 }}\n        whileTap={{ scale: disabled ? 1 : 0.95 }}\n        transition={{ type: \"spring\", stiffness: 400, damping: 17 }}\n      >\n        {children}\n      </motion.a>\n    );\n  }\n\n  return (\n    <MotionComponent\n      className={baseClasses}\n      onClick={onClick}\n      disabled={disabled}\n      whileHover={{ scale: disabled ? 1 : 1.05 }}\n      whileTap={{ scale: disabled ? 1 : 0.95 }}\n      transition={{ type: \"spring\", stiffness: 400, damping: 17 }}\n    >\n      {children}\n    </MotionComponent>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAiBA,MAAM,iBAAiB;IACrB,SAAS;IACT,WAAW;IACX,SAAS;IACT,OAAO;AACT;AAEA,MAAM,cAAc;IAClB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEe,SAAS,OAAO,EAC7B,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,OAAO,EACP,WAAW,KAAK,EAChB,IAAI,EACJ,MAAM,EACN,GAAG,EACS;IACZ,MAAM,cAAc,CAAC;;;;;;IAMnB,EAAE,cAAc,CAAC,QAAQ,CAAC;IAC1B,EAAE,WAAW,CAAC,KAAK,CAAC;IACpB,EAAE,UAAU;EACd,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC,QAAQ;IAEzB,MAAM,kBAAkB,6LAAA,CAAA,SAAM,CAAC,MAAM;IAErC,IAAI,MAAM;QACR,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;YACP,MAAM;YACN,QAAQ;YACR,KAAK;YACL,WAAW;YACX,YAAY;gBAAE,OAAO,WAAW,IAAI;YAAK;YACzC,UAAU;gBAAE,OAAO,WAAW,IAAI;YAAK;YACvC,YAAY;gBAAE,MAAM;gBAAU,WAAW;gBAAK,SAAS;YAAG;sBAEzD;;;;;;IAGP;IAEA,qBACE,6LAAC;QACC,WAAW;QACX,SAAS;QACT,UAAU;QACV,YAAY;YAAE,OAAO,WAAW,IAAI;QAAK;QACzC,UAAU;YAAE,OAAO,WAAW,IAAI;QAAK;QACvC,YAAY;YAAE,MAAM;YAAU,WAAW;YAAK,SAAS;QAAG;kBAEzD;;;;;;AAGP;KApDwB", "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/Property-Plaza-presentation/property-plaza-app/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { ReactNode } from 'react';\n\ninterface CardProps {\n  children: ReactNode;\n  className?: string;\n  hover?: boolean;\n  delay?: number;\n}\n\ninterface PillarCardProps {\n  icon: ReactNode;\n  title: string;\n  description: string;\n  delay?: number;\n  className?: string;\n}\n\nexport default function Card({ \n  children, \n  className = '', \n  hover = true,\n  delay = 0 \n}: CardProps) {\n  return (\n    <motion.div\n      className={`\n        bg-white rounded-2xl p-6 shadow-lg\n        border border-primary/10\n        ${hover ? 'hover:shadow-xl hover:border-primary/20' : ''}\n        transition-all duration-300\n        ${className}\n      `.trim().replace(/\\s+/g, ' ')}\n      initial={{ opacity: 0, y: 30 }}\n      whileInView={{ opacity: 1, y: 0 }}\n      transition={{ \n        duration: 0.6, \n        delay,\n        ease: \"easeOut\" \n      }}\n      viewport={{ once: true, amount: 0.3 }}\n      whileHover={hover ? { \n        y: -5,\n        transition: { type: \"spring\", stiffness: 300, damping: 20 }\n      } : {}}\n    >\n      {children}\n    </motion.div>\n  );\n}\n\nexport function PillarCard({ \n  icon, \n  title, \n  description, \n  delay = 0,\n  className = '' \n}: PillarCardProps) {\n  return (\n    <Card delay={delay} className={className}>\n      <div className=\"text-center space-y-4\">\n        <motion.div \n          className=\"w-16 h-16 bg-primary/10 rounded-full mx-auto flex items-center justify-center text-2xl\"\n          whileHover={{ \n            scale: 1.1,\n            backgroundColor: \"var(--primary)\",\n            color: \"white\"\n          }}\n          transition={{ duration: 0.3 }}\n        >\n          {icon}\n        </motion.div>\n        <h3 className=\"text-xl font-bold text-text-default\">\n          {title}\n        </h3>\n        <p className=\"text-text-muted leading-relaxed\">\n          {description}\n        </p>\n      </div>\n    </Card>\n  );\n}\n\ninterface StatCardProps {\n  number: string;\n  label: string;\n  description?: string;\n  delay?: number;\n}\n\nexport function StatCard({ \n  number, \n  label, \n  description, \n  delay = 0 \n}: StatCardProps) {\n  return (\n    <Card delay={delay} className=\"text-center\">\n      <motion.div\n        className=\"text-4xl md:text-5xl font-bold text-primary mb-2\"\n        initial={{ scale: 0 }}\n        whileInView={{ scale: 1 }}\n        transition={{ \n          type: \"spring\", \n          stiffness: 200, \n          damping: 15,\n          delay: delay + 0.2 \n        }}\n        viewport={{ once: true }}\n      >\n        {number}\n      </motion.div>\n      <h4 className=\"text-lg font-semibold text-text-default mb-1\">\n        {label}\n      </h4>\n      {description && (\n        <p className=\"text-sm text-text-muted\">\n          {description}\n        </p>\n      )}\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAoBe,SAAS,KAAK,EAC3B,QAAQ,EACR,YAAY,EAAE,EACd,QAAQ,IAAI,EACZ,QAAQ,CAAC,EACC;IACV,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC;;;QAGV,EAAE,QAAQ,4CAA4C,GAAG;;QAEzD,EAAE,UAAU;MACd,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC,QAAQ;QACzB,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YACV,UAAU;YACV;YACA,MAAM;QACR;QACA,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAI;QACpC,YAAY,QAAQ;YAClB,GAAG,CAAC;YACJ,YAAY;gBAA<PERSON>,MAAM;gBAAU,WAAW;gBAAK,SAAS;YAAG;QAC5D,IAAI,CAAC;kBAEJ;;;;;;AAGP;KA/BwB;AAiCjB,SAAS,WAAW,EACzB,IAAI,EACJ,KAAK,EACL,WAAW,EACX,QAAQ,CAAC,EACT,YAAY,EAAE,EACE;IAChB,qBACE,6LAAC;QAAK,OAAO;QAAO,WAAW;kBAC7B,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,YAAY;wBACV,OAAO;wBACP,iBAAiB;wBACjB,OAAO;oBACT;oBACA,YAAY;wBAAE,UAAU;oBAAI;8BAE3B;;;;;;8BAEH,6LAAC;oBAAG,WAAU;8BACX;;;;;;8BAEH,6LAAC;oBAAE,WAAU;8BACV;;;;;;;;;;;;;;;;;AAKX;MA9BgB;AAuCT,SAAS,SAAS,EACvB,MAAM,EACN,KAAK,EACL,WAAW,EACX,QAAQ,CAAC,EACK;IACd,qBACE,6LAAC;QAAK,OAAO;QAAO,WAAU;;0BAC5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,OAAO;gBAAE;gBACpB,aAAa;oBAAE,OAAO;gBAAE;gBACxB,YAAY;oBACV,MAAM;oBACN,WAAW;oBACX,SAAS;oBACT,OAAO,QAAQ;gBACjB;gBACA,UAAU;oBAAE,MAAM;gBAAK;0BAEtB;;;;;;0BAEH,6LAAC;gBAAG,WAAU;0BACX;;;;;;YAEF,6BACC,6LAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;MAhCgB", "debugId": null}}, {"offset": {"line": 423, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/Property-Plaza-presentation/property-plaza-app/src/components/ui/Typography.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { ReactNode } from 'react';\n\ninterface TypographyProps {\n  children: ReactNode;\n  className?: string;\n  animate?: boolean;\n  delay?: number;\n}\n\ninterface HeadingProps extends TypographyProps {\n  level?: 1 | 2 | 3 | 4 | 5 | 6;\n  gradient?: boolean;\n}\n\nexport function Heading({ \n  children, \n  level = 1, \n  className = '', \n  animate = true,\n  delay = 0,\n  gradient = false \n}: HeadingProps) {\n  const Tag = `h${level}` as keyof JSX.IntrinsicElements;\n  \n  const baseClasses = {\n    1: 'text-5xl md:text-7xl font-bold leading-tight',\n    2: 'text-4xl md:text-6xl font-bold leading-tight',\n    3: 'text-3xl md:text-4xl font-bold leading-snug',\n    4: 'text-2xl md:text-3xl font-semibold leading-snug',\n    5: 'text-xl md:text-2xl font-semibold leading-normal',\n    6: 'text-lg md:text-xl font-semibold leading-normal'\n  };\n\n  const gradientClass = gradient \n    ? 'bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent' \n    : 'text-text-default';\n\n  const classes = `${baseClasses[level]} ${gradientClass} ${className}`;\n\n  if (!animate) {\n    return <Tag className={classes}>{children}</Tag>;\n  }\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 30 }}\n      whileInView={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.8, delay, ease: \"easeOut\" }}\n      viewport={{ once: true, amount: 0.3 }}\n    >\n      <Tag className={classes}>{children}</Tag>\n    </motion.div>\n  );\n}\n\nexport function Paragraph({ \n  children, \n  className = '', \n  animate = true,\n  delay = 0 \n}: TypographyProps) {\n  const classes = `text-lg md:text-xl text-text-muted leading-relaxed ${className}`;\n\n  if (!animate) {\n    return <p className={classes}>{children}</p>;\n  }\n\n  return (\n    <motion.p\n      className={classes}\n      initial={{ opacity: 0, y: 20 }}\n      whileInView={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.6, delay, ease: \"easeOut\" }}\n      viewport={{ once: true, amount: 0.3 }}\n    >\n      {children}\n    </motion.p>\n  );\n}\n\nexport function Quote({ \n  children, \n  author, \n  className = '', \n  animate = true,\n  delay = 0 \n}: TypographyProps & { author?: string }) {\n  if (!animate) {\n    return (\n      <blockquote className={`text-2xl md:text-3xl font-medium text-text-default italic text-center ${className}`}>\n        \"{children}\"\n        {author && (\n          <footer className=\"text-lg text-text-muted mt-4 not-italic\">\n            — {author}\n          </footer>\n        )}\n      </blockquote>\n    );\n  }\n\n  return (\n    <motion.blockquote\n      className={`text-2xl md:text-3xl font-medium text-text-default italic text-center ${className}`}\n      initial={{ opacity: 0, scale: 0.9 }}\n      whileInView={{ opacity: 1, scale: 1 }}\n      transition={{ duration: 0.8, delay, ease: \"easeOut\" }}\n      viewport={{ once: true, amount: 0.3 }}\n    >\n      <motion.span\n        initial={{ opacity: 0 }}\n        whileInView={{ opacity: 1 }}\n        transition={{ duration: 0.6, delay: delay + 0.2 }}\n        viewport={{ once: true }}\n      >\n        \"{children}\"\n      </motion.span>\n      {author && (\n        <motion.footer\n          className=\"text-lg text-text-muted mt-4 not-italic\"\n          initial={{ opacity: 0, y: 10 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: delay + 0.4 }}\n          viewport={{ once: true }}\n        >\n          — {author}\n        </motion.footer>\n      )}\n    </motion.blockquote>\n  );\n}\n\nexport function Badge({ \n  children, \n  variant = 'primary',\n  className = '' \n}: { \n  children: ReactNode; \n  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';\n  className?: string;\n}) {\n  const variants = {\n    primary: 'bg-primary/10 text-primary border-primary/20',\n    secondary: 'bg-accent/10 text-accent border-accent/20',\n    success: 'bg-success/10 text-success border-success/20',\n    warning: 'bg-warning/10 text-warning border-warning/20',\n    error: 'bg-error/10 text-error border-error/20'\n  };\n\n  return (\n    <span className={`\n      inline-flex items-center px-3 py-1 rounded-full text-sm font-medium\n      border ${variants[variant]} ${className}\n    `.trim().replace(/\\s+/g, ' ')}>\n      {children}\n    </span>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AAFA;;;AAiBO,SAAS,QAAQ,EACtB,QAAQ,EACR,QAAQ,CAAC,EACT,YAAY,EAAE,EACd,UAAU,IAAI,EACd,QAAQ,CAAC,EACT,WAAW,KAAK,EACH;IACb,MAAM,MAAM,CAAC,CAAC,EAAE,OAAO;IAEvB,MAAM,cAAc;QAClB,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;IACL;IAEA,MAAM,gBAAgB,WAClB,0EACA;IAEJ,MAAM,UAAU,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,EAAE,cAAc,CAAC,EAAE,WAAW;IAErE,IAAI,CAAC,SAAS;QACZ,qBAAO,6LAAC;YAAI,WAAW;sBAAU;;;;;;IACnC;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK;YAAO,MAAM;QAAU;QACpD,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAI;kBAEpC,cAAA,6LAAC;YAAI,WAAW;sBAAU;;;;;;;;;;;AAGhC;KAvCgB;AAyCT,SAAS,UAAU,EACxB,QAAQ,EACR,YAAY,EAAE,EACd,UAAU,IAAI,EACd,QAAQ,CAAC,EACO;IAChB,MAAM,UAAU,CAAC,mDAAmD,EAAE,WAAW;IAEjF,IAAI,CAAC,SAAS;QACZ,qBAAO,6LAAC;YAAE,WAAW;sBAAU;;;;;;IACjC;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;QACP,WAAW;QACX,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK;YAAO,MAAM;QAAU;QACpD,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAI;kBAEnC;;;;;;AAGP;MAvBgB;AAyBT,SAAS,MAAM,EACpB,QAAQ,EACR,MAAM,EACN,YAAY,EAAE,EACd,UAAU,IAAI,EACd,QAAQ,CAAC,EAC6B;IACtC,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAW,WAAW,CAAC,sEAAsE,EAAE,WAAW;;gBAAE;gBACzG;gBAAS;gBACV,wBACC,6LAAC;oBAAO,WAAU;;wBAA0C;wBACvD;;;;;;;;;;;;;IAKb;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,UAAU;QAChB,WAAW,CAAC,sEAAsE,EAAE,WAAW;QAC/F,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,aAAa;YAAE,SAAS;YAAG,OAAO;QAAE;QACpC,YAAY;YAAE,UAAU;YAAK;YAAO,MAAM;QAAU;QACpD,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAI;;0BAEpC,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,aAAa;oBAAE,SAAS;gBAAE;gBAC1B,YAAY;oBAAE,UAAU;oBAAK,OAAO,QAAQ;gBAAI;gBAChD,UAAU;oBAAE,MAAM;gBAAK;;oBACxB;oBACG;oBAAS;;;;;;;YAEZ,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,aAAa;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAChC,YAAY;oBAAE,UAAU;oBAAK,OAAO,QAAQ;gBAAI;gBAChD,UAAU;oBAAE,MAAM;gBAAK;;oBACxB;oBACI;;;;;;;;;;;;;AAKb;MAjDgB;AAmDT,SAAS,MAAM,EACpB,QAAQ,EACR,UAAU,SAAS,EACnB,YAAY,EAAE,EAKf;IACC,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,SAAS;QACT,OAAO;IACT;IAEA,qBACE,6LAAC;QAAK,WAAW,CAAC;;aAET,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,UAAU;IAC1C,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC,QAAQ;kBACtB;;;;;;AAGP;MAzBgB", "debugId": null}}, {"offset": {"line": 667, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/Property-Plaza-presentation/property-plaza-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport ScrollContainer, { Section } from '@/components/ui/ScrollContainer';\nimport SlideIndicator from '@/components/ui/SlideIndicator';\nimport Button from '@/components/ui/Button';\nimport { PillarCard } from '@/components/ui/Card';\nimport { Heading, Paragraph } from '@/components/ui/Typography';\nimport { FloatingElements } from '@/components/animations/BackgroundAnimation';\n\nexport default function Home() {\n  const [currentSlide, setCurrentSlide] = useState(1);\n  const totalSlides = 9;\n\n  // Track scroll position to update current slide\n  useEffect(() => {\n    const handleScroll = () => {\n      const scrollPosition = window.scrollY;\n      const windowHeight = window.innerHeight;\n      const newSlide = Math.floor(scrollPosition / windowHeight) + 1;\n      setCurrentSlide(Math.min(newSlide, totalSlides));\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  return (\n    <ScrollContainer>\n      {/* Slide Indicator */}\n      <SlideIndicator currentSlide={currentSlide} totalSlides={totalSlides} />\n\n      {/* 1. Hero Section */}\n      <Section id=\"hero\" className=\"bg-gradient-to-br from-background to-primary/10\">\n        <div className=\"text-center space-y-8\">\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 1, delay: 0.2 }}\n          >\n            <Heading level={1} animate={false} gradient>\n              Empower Property Decisions in Bali\n            </Heading>\n          </motion.div>\n\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 1, delay: 0.4 }}\n            className=\"max-w-3xl mx-auto\"\n          >\n            <Paragraph animate={false} className=\"text-xl md:text-2xl\">\n              Transparency. Knowledge. Connection. Empowerment.\n            </Paragraph>\n          </motion.div>\n\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 1, delay: 0.6 }}\n          >\n            <Button size=\"lg\" className=\"text-lg\">\n              Start the Experience\n            </Button>\n          </motion.div>\n        </div>\n      </Section>\n\n      {/* 2. Market Problem */}\n      <Section id=\"problem\" className=\"bg-white\">\n        <div className=\"text-center space-y-12\">\n          <Heading level={2}>\n            The Market Problem\n          </Heading>\n\n          <div className=\"grid md:grid-cols-3 gap-8 mt-16\">\n            <PillarCard\n              icon=\"❓\"\n              title=\"Buyer Confusion\"\n              description=\"Confused by leasehold vs ownership rules and complex legal structures\"\n              delay={0.1}\n            />\n            <PillarCard\n              icon=\"🚫\"\n              title=\"Limited Access\"\n              description=\"Sellers lack access to serious foreign buyers and qualified leads\"\n              delay={0.2}\n            />\n            <PillarCard\n              icon=\"⚠️\"\n              title=\"Trust Issues\"\n              description=\"Low trust in the real estate space due to lack of transparency\"\n              delay={0.3}\n            />\n          </div>\n        </div>\n      </Section>\n\n      {/* Placeholder sections for remaining slides */}\n      {[3, 4, 5, 6, 7, 8, 9].map((slideNum) => (\n        <Section key={slideNum} id={`slide-${slideNum}`} className=\"bg-background\">\n          <div className=\"text-center\">\n            <h2 className=\"text-4xl font-bold text-text-default\">\n              Section {slideNum} - Coming Soon\n            </h2>\n            <p className=\"text-text-muted mt-4\">\n              This section will be developed in the next phase\n            </p>\n          </div>\n        </Section>\n      ))}\n    </ScrollContainer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAWe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,cAAc;IAEpB,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM;+CAAe;oBACnB,MAAM,iBAAiB,OAAO,OAAO;oBACrC,MAAM,eAAe,OAAO,WAAW;oBACvC,MAAM,WAAW,KAAK,KAAK,CAAC,iBAAiB,gBAAgB;oBAC7D,gBAAgB,KAAK,GAAG,CAAC,UAAU;gBACrC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;kCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;yBAAG,EAAE;IAEL,qBACE,6LAAC,8IAAA,CAAA,UAAe;;0BAEd,6LAAC,6IAAA,CAAA,UAAc;gBAAC,cAAc;gBAAc,aAAa;;;;;;0BAGzD,6LAAC,8IAAA,CAAA,UAAO;gBAAC,IAAG;gBAAO,WAAU;0BAC3B,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAG,OAAO;4BAAI;sCAEtC,cAAA,6LAAC,yIAAA,CAAA,UAAO;gCAAC,OAAO;gCAAG,SAAS;gCAAO,QAAQ;0CAAC;;;;;;;;;;;sCAK9C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAG,OAAO;4BAAI;4BACtC,WAAU;sCAEV,cAAA,6LAAC,yIAAA,CAAA,YAAS;gCAAC,SAAS;gCAAO,WAAU;0CAAsB;;;;;;;;;;;sCAK7D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAG,OAAO;4BAAI;sCAEtC,cAAA,6LAAC,qIAAA,CAAA,UAAM;gCAAC,MAAK;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;0BAQ5C,6LAAC,8IAAA,CAAA,UAAO;gBAAC,IAAG;gBAAU,WAAU;0BAC9B,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yIAAA,CAAA,UAAO;4BAAC,OAAO;sCAAG;;;;;;sCAInB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mIAAA,CAAA,aAAU;oCACT,MAAK;oCACL,OAAM;oCACN,aAAY;oCACZ,OAAO;;;;;;8CAET,6LAAC,mIAAA,CAAA,aAAU;oCACT,MAAK;oCACL,OAAM;oCACN,aAAY;oCACZ,OAAO;;;;;;8CAET,6LAAC,mIAAA,CAAA,aAAU;oCACT,MAAK;oCACL,OAAM;oCACN,aAAY;oCACZ,OAAO;;;;;;;;;;;;;;;;;;;;;;;YAOd;gBAAC;gBAAG;gBAAG;gBAAG;gBAAG;gBAAG;gBAAG;aAAE,CAAC,GAAG,CAAC,CAAC,yBAC1B,6LAAC,8IAAA,CAAA,UAAO;oBAAgB,IAAI,CAAC,MAAM,EAAE,UAAU;oBAAE,WAAU;8BACzD,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAuC;oCAC1C;oCAAS;;;;;;;0CAEpB,6LAAC;gCAAE,WAAU;0CAAuB;;;;;;;;;;;;mBAL1B;;;;;;;;;;;AAatB;GAvGwB;KAAA", "debugId": null}}]}