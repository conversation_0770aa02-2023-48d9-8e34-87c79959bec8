'use client';

import { motion } from 'framer-motion';
import { LogoWithText } from '@/components/ui/Logo';

export default function PartnershipPreview() {
  return (
    <motion.div
      className="mt-16 pt-12 border-t border-white/20"
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: 0.2 }}
      viewport={{ once: true, amount: 0.3 }}
    >
      <motion.div
        className="text-center mb-8"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
      >
        <h3 className="text-lg md:text-xl font-semibold text-text-default mb-2">
          Strategic Partnership
        </h3>
        <p className="text-sm md:text-base text-text-muted">
          Combining expertise for transparent property solutions
        </p>
      </motion.div>

      {/* Partnership Visual */}
      <div className="flex flex-col md:flex-row items-center justify-center space-y-8 md:space-y-0 md:space-x-12">
        {/* Property Plaza */}
        <motion.div
          className="flex flex-col items-center space-y-4"
          initial={{ opacity: 0, x: -30 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
        >
          <div className="p-6 rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20">
            <LogoWithText size="md" animated={false} />
          </div>
          <div className="text-center">
            <h4 className="font-semibold text-text-default">Property Plaza</h4>
            <p className="text-sm text-text-muted">Digital Platform & Reach</p>
          </div>
        </motion.div>

        {/* Connection Animation */}
        <motion.div
          className="flex items-center justify-center"
          initial={{ opacity: 0, scale: 0 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="relative">
            {/* Animated Connection Line */}
            <motion.div
              className="w-16 md:w-24 h-1 bg-gradient-to-r from-primary to-accent rounded-full"
              initial={{ scaleX: 0 }}
              whileInView={{ scaleX: 1 }}
              transition={{ duration: 1, delay: 0.8 }}
              viewport={{ once: true }}
            />
            
            {/* Plus Icon */}
            <motion.div
              className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white font-bold"
              initial={{ rotate: 0, scale: 0 }}
              whileInView={{ rotate: 360, scale: 1 }}
              transition={{ duration: 0.8, delay: 1 }}
              viewport={{ once: true }}
            >
              +
            </motion.div>
          </div>
        </motion.div>

        {/* Paradise Indonesia */}
        <motion.div
          className="flex flex-col items-center space-y-4"
          initial={{ opacity: 0, x: 30 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <div className="p-6 rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-br from-accent to-primary rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-lg">PI</span>
              </div>
              <div>
                <div className="font-bold text-lg text-text-default">Paradise</div>
                <div className="font-bold text-lg text-accent">Indonesia</div>
              </div>
            </div>
          </div>
          <div className="text-center">
            <h4 className="font-semibold text-text-default">Paradise Indonesia</h4>
            <p className="text-sm text-text-muted">Legal Expertise & Trust</p>
          </div>
        </motion.div>
      </div>

      {/* Partnership Benefits */}
      <motion.div
        className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6"
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 1.2 }}
        viewport={{ once: true }}
      >
        {[
          {
            icon: "🎯",
            title: "Targeted Reach",
            description: "Access to serious international buyers"
          },
          {
            icon: "⚖️",
            title: "Legal Certainty",
            description: "Expert guidance on Indonesian property law"
          },
          {
            icon: "🤝",
            title: "Trusted Partnership",
            description: "Combining digital innovation with legal expertise"
          }
        ].map((benefit, index) => (
          <motion.div
            key={benefit.title}
            className="text-center p-4 rounded-xl bg-white/5 backdrop-blur-sm border border-white/10"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1.4 + index * 0.1 }}
            viewport={{ once: true }}
            whileHover={{ 
              y: -5,
              backgroundColor: "rgba(255, 255, 255, 0.1)"
            }}
          >
            <div className="text-2xl mb-3">{benefit.icon}</div>
            <h5 className="font-semibold text-text-default mb-2">{benefit.title}</h5>
            <p className="text-sm text-text-muted">{benefit.description}</p>
          </motion.div>
        ))}
      </motion.div>
    </motion.div>
  );
}
