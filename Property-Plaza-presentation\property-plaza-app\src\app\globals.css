@import "tailwindcss";

:root {
  /* Property Plaza Brand Colors */
  --primary: #b78b4c;
  --accent: #8a6b3c;
  --background: #f7f1eb;
  --text-default: #1e1e1e;
  --text-muted: #666666;

  /* Light theme colors */
  --foreground: var(--text-default);
}

@theme inline {
  /* Property Plaza Brand System */
  --color-primary: var(--primary);
  --color-accent: var(--accent);
  --color-background: var(--background);
  --color-text-default: var(--text-default);
  --color-text-muted: var(--text-muted);

  /* Standard colors */
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* Custom spacing for better design */
  --spacing-section: 6rem;
  --spacing-card: 2rem;
}

@media (prefers-color-scheme: dark) {
  :root {
    /* Keep Property Plaza colors consistent in dark mode */
    --background: #f7f1eb;
    --foreground: #1e1e1e;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  scroll-behavior: smooth;
}

/* Scroll snap for full-screen sections */
html {
  scroll-snap-type: y mandatory;
}

.section-snap {
  scroll-snap-align: start;
  min-height: 100vh;
}
