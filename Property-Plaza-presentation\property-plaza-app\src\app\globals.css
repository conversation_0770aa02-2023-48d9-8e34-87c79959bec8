@import "tailwindcss";

:root {
  /* Property Plaza Brand Colors */
  --primary: #b78b4c;
  --accent: #8a6b3c;
  --background: #f7f1eb;
  --text-default: #1e1e1e;
  --text-muted: #666666;

  /* Light theme colors */
  --foreground: var(--text-default);
}

@theme inline {
  /* Property Plaza Brand System */
  --color-primary: var(--primary);
  --color-accent: var(--accent);
  --color-background: var(--background);
  --color-text-default: var(--text-default);
  --color-text-muted: var(--text-muted);

  /* Additional brand colors */
  --color-primary-light: #c9a366;
  --color-primary-dark: #9d7a42;
  --color-accent-light: #a17d4a;
  --color-accent-dark: #6f5a32;
  --color-white: #ffffff;
  --color-black: #000000;

  /* Semantic colors */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;

  /* Standard colors */
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* Typography scale */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  --font-size-6xl: 3.75rem;
  --font-size-7xl: 4.5rem;
  --font-size-8xl: 6rem;
  --font-size-9xl: 8rem;

  /* Line heights */
  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* Font weights */
  --font-weight-thin: 100;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;

  /* Custom spacing for better design */
  --spacing-section: 6rem;
  --spacing-card: 2rem;
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;
  --spacing-2xl: 4rem;
  --spacing-3xl: 6rem;

  /* Border radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
}

@media (prefers-color-scheme: dark) {
  :root {
    /* Keep Property Plaza colors consistent in dark mode */
    --background: #f7f1eb;
    --foreground: #1e1e1e;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  scroll-behavior: smooth;
}

/* Scroll snap for full-screen sections */
html {
  scroll-snap-type: y mandatory;
  scroll-behavior: smooth;
}

body {
  overflow-x: hidden;
}

.section-snap {
  scroll-snap-align: start;
  scroll-snap-stop: always;
  min-height: 100vh;
}

/* Smooth scrolling for all browsers */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}

/* Disable scroll snap on mobile for better touch experience */
@media (max-width: 768px) {
  html {
    scroll-snap-type: none;
  }

  .section-snap {
    scroll-snap-align: none;
    scroll-snap-stop: normal;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background);
}

::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
}
