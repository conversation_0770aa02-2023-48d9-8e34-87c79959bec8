'use client';

import { motion, useInView } from 'framer-motion';
import { useRef, useState, useEffect } from 'react';

interface StatItemProps {
  number: string;
  label: string;
  suffix?: string;
  delay?: number;
}

function StatItem({ number, label, suffix = '', delay = 0 }: StatItemProps) {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true });
  const [count, setCount] = useState(0);
  
  const targetNumber = parseInt(number.replace(/[^\d]/g, ''));

  useEffect(() => {
    if (isInView) {
      const timer = setTimeout(() => {
        let start = 0;
        const increment = targetNumber / 30;
        const counter = setInterval(() => {
          start += increment;
          if (start >= targetNumber) {
            setCount(targetNumber);
            clearInterval(counter);
          } else {
            setCount(Math.floor(start));
          }
        }, 50);
        
        return () => clearInterval(counter);
      }, delay * 1000);
      
      return () => clearTimeout(timer);
    }
  }, [isInView, targetNumber, delay]);

  return (
    <motion.div
      ref={ref}
      className="text-center"
      initial={{ opacity: 0, y: 20 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
      transition={{ duration: 0.6, delay }}
    >
      <motion.div
        className="text-3xl md:text-4xl font-bold text-primary mb-2"
        initial={{ scale: 0 }}
        animate={isInView ? { scale: 1 } : { scale: 0 }}
        transition={{ 
          type: "spring", 
          stiffness: 200, 
          damping: 15,
          delay: delay + 0.2 
        }}
      >
        {count}{suffix}
      </motion.div>
      <div className="text-sm md:text-base text-text-muted font-medium">
        {label}
      </div>
    </motion.div>
  );
}

export default function HeroStats() {
  return (
    <motion.div
      className="mt-16 pt-12 border-t border-white/20"
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      transition={{ duration: 0.8, delay: 0.5 }}
      viewport={{ once: true, amount: 0.3 }}
    >
      <motion.div
        className="text-center mb-8"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
      >
        <h3 className="text-lg md:text-xl font-semibold text-text-default mb-2">
          Property Plaza Impact
        </h3>
        <p className="text-sm md:text-base text-text-muted">
          Building trust in Bali's real estate market
        </p>
      </motion.div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
        <StatItem
          number="30"
          label="Days Since Launch"
          delay={0.1}
        />
        <StatItem
          number="30"
          suffix="+"
          label="Active Listings"
          delay={0.2}
        />
        <StatItem
          number="350"
          suffix="+"
          label="Unique Visitors"
          delay={0.3}
        />
        <StatItem
          number="5"
          label="Languages Supported"
          delay={0.4}
        />
      </div>

      {/* Trust Indicators */}
      <motion.div
        className="mt-12 flex flex-wrap justify-center items-center gap-6 md:gap-8"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.6 }}
        viewport={{ once: true }}
      >
        {[
          { icon: "🔒", text: "Legal Compliance" },
          { icon: "🌐", text: "Multilingual Support" },
          { icon: "🤖", text: "AI-Assisted" },
          { icon: "📊", text: "Data-Driven" }
        ].map((feature, index) => (
          <motion.div
            key={feature.text}
            className="flex items-center space-x-2 px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20"
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ 
              duration: 0.5, 
              delay: 0.8 + index * 0.1,
              type: "spring",
              stiffness: 200
            }}
            viewport={{ once: true }}
            whileHover={{ 
              scale: 1.05,
              backgroundColor: "rgba(255, 255, 255, 0.2)"
            }}
          >
            <span className="text-lg">{feature.icon}</span>
            <span className="text-sm font-medium text-text-default">
              {feature.text}
            </span>
          </motion.div>
        ))}
      </motion.div>
    </motion.div>
  );
}
