'use client';

import { motion } from 'framer-motion';

interface SlideIndicatorProps {
  currentSlide: number;
  totalSlides: number;
  className?: string;
}

const sectionTitles = [
  'Hero',
  'Problem',
  'Mission',
  'About',
  'Partnership',
  'Synergy',
  'Pilot',
  'Metrics',
  'CTA'
];

export default function SlideIndicator({
  currentSlide,
  totalSlides,
  className = ''
}: SlideIndicatorProps) {
  const progress = (currentSlide / totalSlides) * 100;

  return (
    <motion.div
      className={`fixed bottom-8 right-8 z-50 bg-white/10 backdrop-blur-md rounded-2xl p-4 ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 1 }}
    >
      <div className="flex flex-col items-end space-y-3">
        {/* Current section info */}
        <div className="text-right">
          <div className="text-sm font-medium text-text-default">
            {sectionTitles[currentSlide - 1] || 'Section'}
          </div>
          <div className="text-xs text-text-muted">
            {currentSlide} of {totalSlides}
          </div>
        </div>

        {/* Progress bar */}
        <div className="w-24 h-1 bg-text-muted/20 rounded-full overflow-hidden">
          <motion.div
            className="h-full bg-primary rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${progress}%` }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          />
        </div>

        {/* Dot indicators */}
        <div className="flex space-x-1">
          {Array.from({ length: totalSlides }, (_, i) => (
            <motion.div
              key={i}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                i + 1 === currentSlide
                  ? 'bg-primary scale-125'
                  : 'bg-text-muted/30'
              }`}
              whileHover={{ scale: 1.3 }}
              initial={{ scale: 0 }}
              animate={{ scale: i + 1 === currentSlide ? 1.25 : 1 }}
              transition={{
                type: "spring",
                stiffness: 300,
                damping: 20,
                delay: i * 0.1
              }}
            />
          ))}
        </div>
      </div>
    </motion.div>
  );
}
