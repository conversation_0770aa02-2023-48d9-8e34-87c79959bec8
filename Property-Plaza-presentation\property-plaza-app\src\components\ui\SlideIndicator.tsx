'use client';

import { motion } from 'framer-motion';

interface SlideIndicatorProps {
  currentSlide: number;
  totalSlides: number;
  className?: string;
}

export default function SlideIndicator({ 
  currentSlide, 
  totalSlides, 
  className = '' 
}: SlideIndicatorProps) {
  return (
    <motion.div 
      className={`fixed bottom-8 right-8 z-50 bg-white/10 backdrop-blur-md rounded-full px-4 py-2 ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 1 }}
    >
      <div className="flex items-center space-x-2">
        <span className="text-sm font-medium text-text-default">
          Slide {currentSlide} of {totalSlides}
        </span>
        <div className="flex space-x-1">
          {Array.from({ length: totalSlides }, (_, i) => (
            <motion.div
              key={i}
              className={`w-2 h-2 rounded-full transition-colors duration-300 ${
                i + 1 === currentSlide 
                  ? 'bg-primary' 
                  : 'bg-text-muted/30'
              }`}
              whileHover={{ scale: 1.2 }}
            />
          ))}
        </div>
      </div>
    </motion.div>
  );
}
