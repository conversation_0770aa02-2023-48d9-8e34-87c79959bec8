import type { Metadata, Viewport } from "next";
import { Inter, Playfair_Display } from "next/font/google";
import "./globals.css";

const inter = Inter({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: "swap",
});

const playfair = Playfair_Display({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "Property Plaza × Paradise Indonesia | Strategic Partnership Presentation",
  description: "Interactive presentation showcasing the strategic collaboration between Property Plaza and Paradise Indonesia for transparent property decisions in Bali.",
  keywords: "Property Plaza, Paradise Indonesia, Bali real estate, property investment, leasehold, transparency",
  authors: [{ name: "Property Plaza" }],
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${inter.variable} ${playfair.variable}`}>
      <body className="antialiased bg-background text-text-default">
        {children}
      </body>
    </html>
  );
}
