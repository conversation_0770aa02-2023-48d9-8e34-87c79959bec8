{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/Property-Plaza-presentation/property-plaza-app/src/components/ui/ScrollContainer.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { ReactNode } from 'react';\n\ninterface ScrollContainerProps {\n  children: ReactNode;\n  className?: string;\n}\n\nexport default function ScrollContainer({ \n  children, \n  className = '' \n}: ScrollContainerProps) {\n  return (\n    <motion.div \n      className={`scroll-smooth ${className}`}\n      initial={{ opacity: 0 }}\n      animate={{ opacity: 1 }}\n      transition={{ duration: 0.5 }}\n    >\n      {children}\n    </motion.div>\n  );\n}\n\ninterface SectionProps {\n  children: ReactNode;\n  className?: string;\n  id?: string;\n}\n\nexport function Section({ \n  children, \n  className = '', \n  id \n}: SectionProps) {\n  return (\n    <motion.section\n      id={id}\n      className={`section-snap flex flex-col justify-center items-center px-4 md:px-8 lg:px-16 ${className}`}\n      initial={{ opacity: 0, y: 50 }}\n      whileInView={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.8, ease: \"easeOut\" }}\n      viewport={{ once: true, amount: 0.3 }}\n    >\n      <div className=\"max-w-7xl w-full\">\n        {children}\n      </div>\n    </motion.section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAUe,SAAS,gBAAgB,EACtC,QAAQ,EACR,YAAY,EAAE,EACO;IACrB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,cAAc,EAAE,WAAW;QACvC,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,YAAY;YAAE,UAAU;QAAI;kBAE3B;;;;;;AAGP;AAQO,SAAS,QAAQ,EACtB,QAAQ,EACR,YAAY,EAAE,EACd,EAAE,EACW;IACb,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;QACb,IAAI;QACJ,WAAW,CAAC,6EAA6E,EAAE,WAAW;QACtG,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC7C,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAI;kBAEpC,cAAA,8OAAC;YAAI,WAAU;sBACZ;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/Property-Plaza-presentation/property-plaza-app/src/components/ui/SlideIndicator.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\n\ninterface SlideIndicatorProps {\n  currentSlide: number;\n  totalSlides: number;\n  className?: string;\n}\n\nexport default function SlideIndicator({ \n  currentSlide, \n  totalSlides, \n  className = '' \n}: SlideIndicatorProps) {\n  return (\n    <motion.div \n      className={`fixed bottom-8 right-8 z-50 bg-white/10 backdrop-blur-md rounded-full px-4 py-2 ${className}`}\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ delay: 1 }}\n    >\n      <div className=\"flex items-center space-x-2\">\n        <span className=\"text-sm font-medium text-text-default\">\n          Slide {currentSlide} of {totalSlides}\n        </span>\n        <div className=\"flex space-x-1\">\n          {Array.from({ length: totalSlides }, (_, i) => (\n            <motion.div\n              key={i}\n              className={`w-2 h-2 rounded-full transition-colors duration-300 ${\n                i + 1 === currentSlide \n                  ? 'bg-primary' \n                  : 'bg-text-muted/30'\n              }`}\n              whileHover={{ scale: 1.2 }}\n            />\n          ))}\n        </div>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAUe,SAAS,eAAe,EACrC,YAAY,EACZ,WAAW,EACX,YAAY,EAAE,EACM;IACpB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,gFAAgF,EAAE,WAAW;QACzG,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,OAAO;QAAE;kBAEvB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAK,WAAU;;wBAAwC;wBAC/C;wBAAa;wBAAK;;;;;;;8BAE3B,8OAAC;oBAAI,WAAU;8BACZ,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAY,GAAG,CAAC,GAAG,kBACvC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAW,CAAC,oDAAoD,EAC9D,IAAI,MAAM,eACN,eACA,oBACJ;4BACF,YAAY;gCAAE,OAAO;4BAAI;2BANpB;;;;;;;;;;;;;;;;;;;;;AAanB", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/Property-Plaza-presentation/property-plaza-app/src/components/ui/Button.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { ReactNode } from 'react';\n\ninterface ButtonProps {\n  children: ReactNode;\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';\n  size?: 'sm' | 'md' | 'lg' | 'xl';\n  className?: string;\n  onClick?: () => void;\n  disabled?: boolean;\n  href?: string;\n  target?: string;\n  rel?: string;\n}\n\nconst buttonVariants = {\n  primary: 'bg-primary hover:bg-primary-dark text-white shadow-md hover:shadow-lg',\n  secondary: 'bg-accent hover:bg-accent-dark text-white shadow-md hover:shadow-lg',\n  outline: 'border-2 border-primary text-primary hover:bg-primary hover:text-white',\n  ghost: 'text-primary hover:bg-primary/10'\n};\n\nconst buttonSizes = {\n  sm: 'px-4 py-2 text-sm',\n  md: 'px-6 py-3 text-base',\n  lg: 'px-8 py-4 text-lg',\n  xl: 'px-10 py-5 text-xl'\n};\n\nexport default function Button({\n  children,\n  variant = 'primary',\n  size = 'md',\n  className = '',\n  onClick,\n  disabled = false,\n  href,\n  target,\n  rel\n}: ButtonProps) {\n  const baseClasses = `\n    inline-flex items-center justify-center\n    font-semibold rounded-full\n    transition-all duration-300 ease-out\n    focus:outline-none focus:ring-4 focus:ring-primary/20\n    disabled:opacity-50 disabled:cursor-not-allowed\n    ${buttonVariants[variant]}\n    ${buttonSizes[size]}\n    ${className}\n  `.trim().replace(/\\s+/g, ' ');\n\n  const MotionComponent = motion.button;\n\n  if (href) {\n    return (\n      <motion.a\n        href={href}\n        target={target}\n        rel={rel}\n        className={baseClasses}\n        whileHover={{ scale: disabled ? 1 : 1.05 }}\n        whileTap={{ scale: disabled ? 1 : 0.95 }}\n        transition={{ type: \"spring\", stiffness: 400, damping: 17 }}\n      >\n        {children}\n      </motion.a>\n    );\n  }\n\n  return (\n    <MotionComponent\n      className={baseClasses}\n      onClick={onClick}\n      disabled={disabled}\n      whileHover={{ scale: disabled ? 1 : 1.05 }}\n      whileTap={{ scale: disabled ? 1 : 0.95 }}\n      transition={{ type: \"spring\", stiffness: 400, damping: 17 }}\n    >\n      {children}\n    </MotionComponent>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAiBA,MAAM,iBAAiB;IACrB,SAAS;IACT,WAAW;IACX,SAAS;IACT,OAAO;AACT;AAEA,MAAM,cAAc;IAClB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEe,SAAS,OAAO,EAC7B,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,OAAO,EACP,WAAW,KAAK,EAChB,IAAI,EACJ,MAAM,EACN,GAAG,EACS;IACZ,MAAM,cAAc,CAAC;;;;;;IAMnB,EAAE,cAAc,CAAC,QAAQ,CAAC;IAC1B,EAAE,WAAW,CAAC,KAAK,CAAC;IACpB,EAAE,UAAU;EACd,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC,QAAQ;IAEzB,MAAM,kBAAkB,0LAAA,CAAA,SAAM,CAAC,MAAM;IAErC,IAAI,MAAM;QACR,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;YACP,MAAM;YACN,QAAQ;YACR,KAAK;YACL,WAAW;YACX,YAAY;gBAAE,OAAO,WAAW,IAAI;YAAK;YACzC,UAAU;gBAAE,OAAO,WAAW,IAAI;YAAK;YACvC,YAAY;gBAAE,MAAM;gBAAU,WAAW;gBAAK,SAAS;YAAG;sBAEzD;;;;;;IAGP;IAEA,qBACE,8OAAC;QACC,WAAW;QACX,SAAS;QACT,UAAU;QACV,YAAY;YAAE,OAAO,WAAW,IAAI;QAAK;QACzC,UAAU;YAAE,OAAO,WAAW,IAAI;QAAK;QACvC,YAAY;YAAE,MAAM;YAAU,WAAW;YAAK,SAAS;QAAG;kBAEzD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/Property-Plaza-presentation/property-plaza-app/src/components/ui/Typography.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { ReactNode } from 'react';\n\ninterface TypographyProps {\n  children: ReactNode;\n  className?: string;\n  animate?: boolean;\n  delay?: number;\n}\n\ninterface HeadingProps extends TypographyProps {\n  level?: 1 | 2 | 3 | 4 | 5 | 6;\n  gradient?: boolean;\n}\n\nexport function Heading({ \n  children, \n  level = 1, \n  className = '', \n  animate = true,\n  delay = 0,\n  gradient = false \n}: HeadingProps) {\n  const Tag = `h${level}` as keyof JSX.IntrinsicElements;\n  \n  const baseClasses = {\n    1: 'text-5xl md:text-7xl font-bold leading-tight',\n    2: 'text-4xl md:text-6xl font-bold leading-tight',\n    3: 'text-3xl md:text-4xl font-bold leading-snug',\n    4: 'text-2xl md:text-3xl font-semibold leading-snug',\n    5: 'text-xl md:text-2xl font-semibold leading-normal',\n    6: 'text-lg md:text-xl font-semibold leading-normal'\n  };\n\n  const gradientClass = gradient \n    ? 'bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent' \n    : 'text-text-default';\n\n  const classes = `${baseClasses[level]} ${gradientClass} ${className}`;\n\n  if (!animate) {\n    return <Tag className={classes}>{children}</Tag>;\n  }\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 30 }}\n      whileInView={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.8, delay, ease: \"easeOut\" }}\n      viewport={{ once: true, amount: 0.3 }}\n    >\n      <Tag className={classes}>{children}</Tag>\n    </motion.div>\n  );\n}\n\nexport function Paragraph({ \n  children, \n  className = '', \n  animate = true,\n  delay = 0 \n}: TypographyProps) {\n  const classes = `text-lg md:text-xl text-text-muted leading-relaxed ${className}`;\n\n  if (!animate) {\n    return <p className={classes}>{children}</p>;\n  }\n\n  return (\n    <motion.p\n      className={classes}\n      initial={{ opacity: 0, y: 20 }}\n      whileInView={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.6, delay, ease: \"easeOut\" }}\n      viewport={{ once: true, amount: 0.3 }}\n    >\n      {children}\n    </motion.p>\n  );\n}\n\nexport function Quote({ \n  children, \n  author, \n  className = '', \n  animate = true,\n  delay = 0 \n}: TypographyProps & { author?: string }) {\n  if (!animate) {\n    return (\n      <blockquote className={`text-2xl md:text-3xl font-medium text-text-default italic text-center ${className}`}>\n        \"{children}\"\n        {author && (\n          <footer className=\"text-lg text-text-muted mt-4 not-italic\">\n            — {author}\n          </footer>\n        )}\n      </blockquote>\n    );\n  }\n\n  return (\n    <motion.blockquote\n      className={`text-2xl md:text-3xl font-medium text-text-default italic text-center ${className}`}\n      initial={{ opacity: 0, scale: 0.9 }}\n      whileInView={{ opacity: 1, scale: 1 }}\n      transition={{ duration: 0.8, delay, ease: \"easeOut\" }}\n      viewport={{ once: true, amount: 0.3 }}\n    >\n      <motion.span\n        initial={{ opacity: 0 }}\n        whileInView={{ opacity: 1 }}\n        transition={{ duration: 0.6, delay: delay + 0.2 }}\n        viewport={{ once: true }}\n      >\n        \"{children}\"\n      </motion.span>\n      {author && (\n        <motion.footer\n          className=\"text-lg text-text-muted mt-4 not-italic\"\n          initial={{ opacity: 0, y: 10 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: delay + 0.4 }}\n          viewport={{ once: true }}\n        >\n          — {author}\n        </motion.footer>\n      )}\n    </motion.blockquote>\n  );\n}\n\nexport function Badge({ \n  children, \n  variant = 'primary',\n  className = '' \n}: { \n  children: ReactNode; \n  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';\n  className?: string;\n}) {\n  const variants = {\n    primary: 'bg-primary/10 text-primary border-primary/20',\n    secondary: 'bg-accent/10 text-accent border-accent/20',\n    success: 'bg-success/10 text-success border-success/20',\n    warning: 'bg-warning/10 text-warning border-warning/20',\n    error: 'bg-error/10 text-error border-error/20'\n  };\n\n  return (\n    <span className={`\n      inline-flex items-center px-3 py-1 rounded-full text-sm font-medium\n      border ${variants[variant]} ${className}\n    `.trim().replace(/\\s+/g, ' ')}>\n      {children}\n    </span>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AAFA;;;AAiBO,SAAS,QAAQ,EACtB,QAAQ,EACR,QAAQ,CAAC,EACT,YAAY,EAAE,EACd,UAAU,IAAI,EACd,QAAQ,CAAC,EACT,WAAW,KAAK,EACH;IACb,MAAM,MAAM,CAAC,CAAC,EAAE,OAAO;IAEvB,MAAM,cAAc;QAClB,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;IACL;IAEA,MAAM,gBAAgB,WAClB,0EACA;IAEJ,MAAM,UAAU,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,EAAE,cAAc,CAAC,EAAE,WAAW;IAErE,IAAI,CAAC,SAAS;QACZ,qBAAO,8OAAC;YAAI,WAAW;sBAAU;;;;;;IACnC;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK;YAAO,MAAM;QAAU;QACpD,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAI;kBAEpC,cAAA,8OAAC;YAAI,WAAW;sBAAU;;;;;;;;;;;AAGhC;AAEO,SAAS,UAAU,EACxB,QAAQ,EACR,YAAY,EAAE,EACd,UAAU,IAAI,EACd,QAAQ,CAAC,EACO;IAChB,MAAM,UAAU,CAAC,mDAAmD,EAAE,WAAW;IAEjF,IAAI,CAAC,SAAS;QACZ,qBAAO,8OAAC;YAAE,WAAW;sBAAU;;;;;;IACjC;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;QACP,WAAW;QACX,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK;YAAO,MAAM;QAAU;QACpD,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAI;kBAEnC;;;;;;AAGP;AAEO,SAAS,MAAM,EACpB,QAAQ,EACR,MAAM,EACN,YAAY,EAAE,EACd,UAAU,IAAI,EACd,QAAQ,CAAC,EAC6B;IACtC,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAW,WAAW,CAAC,sEAAsE,EAAE,WAAW;;gBAAE;gBACzG;gBAAS;gBACV,wBACC,8OAAC;oBAAO,WAAU;;wBAA0C;wBACvD;;;;;;;;;;;;;IAKb;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,UAAU;QAChB,WAAW,CAAC,sEAAsE,EAAE,WAAW;QAC/F,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,aAAa;YAAE,SAAS;YAAG,OAAO;QAAE;QACpC,YAAY;YAAE,UAAU;YAAK;YAAO,MAAM;QAAU;QACpD,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAI;;0BAEpC,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,aAAa;oBAAE,SAAS;gBAAE;gBAC1B,YAAY;oBAAE,UAAU;oBAAK,OAAO,QAAQ;gBAAI;gBAChD,UAAU;oBAAE,MAAM;gBAAK;;oBACxB;oBACG;oBAAS;;;;;;;YAEZ,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,aAAa;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAChC,YAAY;oBAAE,UAAU;oBAAK,OAAO,QAAQ;gBAAI;gBAChD,UAAU;oBAAE,MAAM;gBAAK;;oBACxB;oBACI;;;;;;;;;;;;;AAKb;AAEO,SAAS,MAAM,EACpB,QAAQ,EACR,UAAU,SAAS,EACnB,YAAY,EAAE,EAKf;IACC,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,SAAS;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QAAK,WAAW,CAAC;;aAET,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,UAAU;IAC1C,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC,QAAQ;kBACtB;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 474, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/Property-Plaza-presentation/property-plaza-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport ScrollContainer, { Section } from '@/components/ui/ScrollContainer';\nimport SlideIndicator from '@/components/ui/SlideIndicator';\nimport Button from '@/components/ui/Button';\nimport { PillarCard } from '@/components/ui/Card';\nimport { Heading, Paragraph } from '@/components/ui/Typography';\n\nexport default function Home() {\n  const [currentSlide, setCurrentSlide] = useState(1);\n  const totalSlides = 9;\n\n  // Track scroll position to update current slide\n  useEffect(() => {\n    const handleScroll = () => {\n      const scrollPosition = window.scrollY;\n      const windowHeight = window.innerHeight;\n      const newSlide = Math.floor(scrollPosition / windowHeight) + 1;\n      setCurrentSlide(Math.min(newSlide, totalSlides));\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  return (\n    <ScrollContainer>\n      {/* Slide Indicator */}\n      <SlideIndicator currentSlide={currentSlide} totalSlides={totalSlides} />\n\n      {/* 1. Hero Section */}\n      <Section id=\"hero\" className=\"bg-gradient-to-br from-background to-primary/10\">\n        <div className=\"text-center space-y-8\">\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 1, delay: 0.2 }}\n          >\n            <Heading level={1} animate={false} gradient>\n              Empower Property Decisions in Bali\n            </Heading>\n          </motion.div>\n\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 1, delay: 0.4 }}\n            className=\"max-w-3xl mx-auto\"\n          >\n            <Paragraph animate={false} className=\"text-xl md:text-2xl\">\n              Transparency. Knowledge. Connection. Empowerment.\n            </Paragraph>\n          </motion.div>\n\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 1, delay: 0.6 }}\n          >\n            <Button size=\"lg\" className=\"text-lg\">\n              Start the Experience\n            </Button>\n          </motion.div>\n        </div>\n      </Section>\n\n      {/* 2. Market Problem */}\n      <Section id=\"problem\" className=\"bg-white\">\n        <div className=\"text-center space-y-8\">\n          <h2 className=\"text-4xl md:text-6xl font-bold text-text-default\">\n            The Market Problem\n          </h2>\n          <div className=\"grid md:grid-cols-3 gap-8 mt-12\">\n            <div className=\"space-y-4\">\n              <div className=\"w-16 h-16 bg-primary/20 rounded-full mx-auto flex items-center justify-center\">\n                <span className=\"text-2xl\">❓</span>\n              </div>\n              <h3 className=\"text-xl font-semibold\">Buyer Confusion</h3>\n              <p className=\"text-text-muted\">Confused by leasehold vs ownership rules</p>\n            </div>\n            <div className=\"space-y-4\">\n              <div className=\"w-16 h-16 bg-primary/20 rounded-full mx-auto flex items-center justify-center\">\n                <span className=\"text-2xl\">🚫</span>\n              </div>\n              <h3 className=\"text-xl font-semibold\">Limited Access</h3>\n              <p className=\"text-text-muted\">Sellers lack access to serious foreign buyers</p>\n            </div>\n            <div className=\"space-y-4\">\n              <div className=\"w-16 h-16 bg-primary/20 rounded-full mx-auto flex items-center justify-center\">\n                <span className=\"text-2xl\">⚠️</span>\n              </div>\n              <h3 className=\"text-xl font-semibold\">Trust Issues</h3>\n              <p className=\"text-text-muted\">Low trust in the real estate space</p>\n            </div>\n          </div>\n        </div>\n      </Section>\n\n      {/* Placeholder sections for remaining slides */}\n      {[3, 4, 5, 6, 7, 8, 9].map((slideNum) => (\n        <Section key={slideNum} id={`slide-${slideNum}`} className=\"bg-background\">\n          <div className=\"text-center\">\n            <h2 className=\"text-4xl font-bold text-text-default\">\n              Section {slideNum} - Coming Soon\n            </h2>\n            <p className=\"text-text-muted mt-4\">\n              This section will be developed in the next phase\n            </p>\n          </div>\n        </Section>\n      ))}\n    </ScrollContainer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AARA;;;;;;;;AAUe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,cAAc;IAEpB,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,MAAM,iBAAiB,OAAO,OAAO;YACrC,MAAM,eAAe,OAAO,WAAW;YACvC,MAAM,WAAW,KAAK,KAAK,CAAC,iBAAiB,gBAAgB;YAC7D,gBAAgB,KAAK,GAAG,CAAC,UAAU;QACrC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,qBACE,8OAAC,2IAAA,CAAA,UAAe;;0BAEd,8OAAC,0IAAA,CAAA,UAAc;gBAAC,cAAc;gBAAc,aAAa;;;;;;0BAGzD,8OAAC,2IAAA,CAAA,UAAO;gBAAC,IAAG;gBAAO,WAAU;0BAC3B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAG,OAAO;4BAAI;sCAEtC,cAAA,8OAAC,sIAAA,CAAA,UAAO;gCAAC,OAAO;gCAAG,SAAS;gCAAO,QAAQ;0CAAC;;;;;;;;;;;sCAK9C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAG,OAAO;4BAAI;4BACtC,WAAU;sCAEV,cAAA,8OAAC,sIAAA,CAAA,YAAS;gCAAC,SAAS;gCAAO,WAAU;0CAAsB;;;;;;;;;;;sCAK7D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAG,OAAO;4BAAI;sCAEtC,cAAA,8OAAC,kIAAA,CAAA,UAAM;gCAAC,MAAK;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;0BAQ5C,8OAAC,2IAAA,CAAA,UAAO;gBAAC,IAAG;gBAAU,WAAU;0BAC9B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmD;;;;;;sCAGjE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,8OAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,8OAAC;4CAAE,WAAU;sDAAkB;;;;;;;;;;;;8CAEjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,8OAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,8OAAC;4CAAE,WAAU;sDAAkB;;;;;;;;;;;;8CAEjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,8OAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,8OAAC;4CAAE,WAAU;sDAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOtC;gBAAC;gBAAG;gBAAG;gBAAG;gBAAG;gBAAG;gBAAG;aAAE,CAAC,GAAG,CAAC,CAAC,yBAC1B,8OAAC,2IAAA,CAAA,UAAO;oBAAgB,IAAI,CAAC,MAAM,EAAE,UAAU;oBAAE,WAAU;8BACzD,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAuC;oCAC1C;oCAAS;;;;;;;0CAEpB,8OAAC;gCAAE,WAAU;0CAAuB;;;;;;;;;;;;mBAL1B;;;;;;;;;;;AAatB", "debugId": null}}]}