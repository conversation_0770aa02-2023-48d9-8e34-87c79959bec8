{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/Property-Plaza-presentation/property-plaza-app/src/components/ui/ScrollContainer.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { ReactNode } from 'react';\n\ninterface ScrollContainerProps {\n  children: ReactNode;\n  className?: string;\n}\n\nexport default function ScrollContainer({ \n  children, \n  className = '' \n}: ScrollContainerProps) {\n  return (\n    <motion.div \n      className={`scroll-smooth ${className}`}\n      initial={{ opacity: 0 }}\n      animate={{ opacity: 1 }}\n      transition={{ duration: 0.5 }}\n    >\n      {children}\n    </motion.div>\n  );\n}\n\ninterface SectionProps {\n  children: ReactNode;\n  className?: string;\n  id?: string;\n}\n\nexport function Section({ \n  children, \n  className = '', \n  id \n}: SectionProps) {\n  return (\n    <motion.section\n      id={id}\n      className={`section-snap flex flex-col justify-center items-center px-4 md:px-8 lg:px-16 ${className}`}\n      initial={{ opacity: 0, y: 50 }}\n      whileInView={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.8, ease: \"easeOut\" }}\n      viewport={{ once: true, amount: 0.3 }}\n    >\n      <div className=\"max-w-7xl w-full\">\n        {children}\n      </div>\n    </motion.section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAUe,SAAS,gBAAgB,EACtC,QAAQ,EACR,YAAY,EAAE,EACO;IACrB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,cAAc,EAAE,WAAW;QACvC,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,YAAY;YAAE,UAAU;QAAI;kBAE3B;;;;;;AAGP;KAdwB;AAsBjB,SAAS,QAAQ,EACtB,QAAQ,EACR,YAAY,EAAE,EACd,EAAE,EACW;IACb,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,OAAO;QACb,IAAI;QACJ,WAAW,CAAC,6EAA6E,EAAE,WAAW;QACtG,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC7C,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAI;kBAEpC,cAAA,6LAAC;YAAI,WAAU;sBACZ;;;;;;;;;;;AAIT;MAnBgB", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/Property-Plaza-presentation/property-plaza-app/src/components/ui/SlideIndicator.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\n\ninterface SlideIndicatorProps {\n  currentSlide: number;\n  totalSlides: number;\n  className?: string;\n}\n\nexport default function SlideIndicator({ \n  currentSlide, \n  totalSlides, \n  className = '' \n}: SlideIndicatorProps) {\n  return (\n    <motion.div \n      className={`fixed bottom-8 right-8 z-50 bg-white/10 backdrop-blur-md rounded-full px-4 py-2 ${className}`}\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ delay: 1 }}\n    >\n      <div className=\"flex items-center space-x-2\">\n        <span className=\"text-sm font-medium text-text-default\">\n          Slide {currentSlide} of {totalSlides}\n        </span>\n        <div className=\"flex space-x-1\">\n          {Array.from({ length: totalSlides }, (_, i) => (\n            <motion.div\n              key={i}\n              className={`w-2 h-2 rounded-full transition-colors duration-300 ${\n                i + 1 === currentSlide \n                  ? 'bg-primary' \n                  : 'bg-text-muted/30'\n              }`}\n              whileHover={{ scale: 1.2 }}\n            />\n          ))}\n        </div>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAUe,SAAS,eAAe,EACrC,YAAY,EACZ,WAAW,EACX,YAAY,EAAE,EACM;IACpB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,gFAAgF,EAAE,WAAW;QACzG,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,OAAO;QAAE;kBAEvB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAK,WAAU;;wBAAwC;wBAC/C;wBAAa;wBAAK;;;;;;;8BAE3B,6LAAC;oBAAI,WAAU;8BACZ,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAY,GAAG,CAAC,GAAG,kBACvC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAW,CAAC,oDAAoD,EAC9D,IAAI,MAAM,eACN,eACA,oBACJ;4BACF,YAAY;gCAAE,OAAO;4BAAI;2BANpB;;;;;;;;;;;;;;;;;;;;;AAanB;KAhCwB", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/Property-Plaza-presentation/property-plaza-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport ScrollContainer, { Section } from '@/components/ui/ScrollContainer';\nimport SlideIndicator from '@/components/ui/SlideIndicator';\n\nexport default function Home() {\n  const [currentSlide, setCurrentSlide] = useState(1);\n  const totalSlides = 9;\n\n  // Track scroll position to update current slide\n  useEffect(() => {\n    const handleScroll = () => {\n      const scrollPosition = window.scrollY;\n      const windowHeight = window.innerHeight;\n      const newSlide = Math.floor(scrollPosition / windowHeight) + 1;\n      setCurrentSlide(Math.min(newSlide, totalSlides));\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  return (\n    <ScrollContainer>\n      {/* Slide Indicator */}\n      <SlideIndicator currentSlide={currentSlide} totalSlides={totalSlides} />\n\n      {/* 1. Hero Section */}\n      <Section id=\"hero\" className=\"bg-gradient-to-br from-background to-primary/10\">\n        <div className=\"text-center space-y-8\">\n          <motion.h1\n            className=\"text-5xl md:text-7xl font-bold text-text-default\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 1, delay: 0.2 }}\n          >\n            Empower Property Decisions in Bali\n          </motion.h1>\n          <motion.p\n            className=\"text-xl md:text-2xl text-text-muted max-w-3xl mx-auto\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 1, delay: 0.4 }}\n          >\n            Transparency. Knowledge. Connection. Empowerment.\n          </motion.p>\n          <motion.button\n            className=\"bg-primary hover:bg-accent text-white px-8 py-4 rounded-full text-lg font-semibold transition-colors duration-300\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 1, delay: 0.6 }}\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            Start the Experience\n          </motion.button>\n        </div>\n      </Section>\n\n      {/* 2. Market Problem */}\n      <Section id=\"problem\" className=\"bg-white\">\n        <div className=\"text-center space-y-8\">\n          <h2 className=\"text-4xl md:text-6xl font-bold text-text-default\">\n            The Market Problem\n          </h2>\n          <div className=\"grid md:grid-cols-3 gap-8 mt-12\">\n            <div className=\"space-y-4\">\n              <div className=\"w-16 h-16 bg-primary/20 rounded-full mx-auto flex items-center justify-center\">\n                <span className=\"text-2xl\">❓</span>\n              </div>\n              <h3 className=\"text-xl font-semibold\">Buyer Confusion</h3>\n              <p className=\"text-text-muted\">Confused by leasehold vs ownership rules</p>\n            </div>\n            <div className=\"space-y-4\">\n              <div className=\"w-16 h-16 bg-primary/20 rounded-full mx-auto flex items-center justify-center\">\n                <span className=\"text-2xl\">🚫</span>\n              </div>\n              <h3 className=\"text-xl font-semibold\">Limited Access</h3>\n              <p className=\"text-text-muted\">Sellers lack access to serious foreign buyers</p>\n            </div>\n            <div className=\"space-y-4\">\n              <div className=\"w-16 h-16 bg-primary/20 rounded-full mx-auto flex items-center justify-center\">\n                <span className=\"text-2xl\">⚠️</span>\n              </div>\n              <h3 className=\"text-xl font-semibold\">Trust Issues</h3>\n              <p className=\"text-text-muted\">Low trust in the real estate space</p>\n            </div>\n          </div>\n        </div>\n      </Section>\n\n      {/* Placeholder sections for remaining slides */}\n      {[3, 4, 5, 6, 7, 8, 9].map((slideNum) => (\n        <Section key={slideNum} id={`slide-${slideNum}`} className=\"bg-background\">\n          <div className=\"text-center\">\n            <h2 className=\"text-4xl font-bold text-text-default\">\n              Section {slideNum} - Coming Soon\n            </h2>\n            <p className=\"text-text-muted mt-4\">\n              This section will be developed in the next phase\n            </p>\n          </div>\n        </Section>\n      ))}\n    </ScrollContainer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,cAAc;IAEpB,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM;+CAAe;oBACnB,MAAM,iBAAiB,OAAO,OAAO;oBACrC,MAAM,eAAe,OAAO,WAAW;oBACvC,MAAM,WAAW,KAAK,KAAK,CAAC,iBAAiB,gBAAgB;oBAC7D,gBAAgB,KAAK,GAAG,CAAC,UAAU;gBACrC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;kCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;yBAAG,EAAE;IAEL,qBACE,6LAAC,8IAAA,CAAA,UAAe;;0BAEd,6LAAC,6IAAA,CAAA,UAAc;gBAAC,cAAc;gBAAc,aAAa;;;;;;0BAGzD,6LAAC,8IAAA,CAAA,UAAO;gBAAC,IAAG;gBAAO,WAAU;0BAC3B,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAG,OAAO;4BAAI;sCACvC;;;;;;sCAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAG,OAAO;4BAAI;sCACvC;;;;;;sCAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAG,OAAO;4BAAI;4BACtC,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;sCACzB;;;;;;;;;;;;;;;;;0BAOL,6LAAC,8IAAA,CAAA,UAAO;gBAAC,IAAG;gBAAU,WAAU;0BAC9B,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmD;;;;;;sCAGjE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,6LAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,6LAAC;4CAAE,WAAU;sDAAkB;;;;;;;;;;;;8CAEjC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,6LAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,6LAAC;4CAAE,WAAU;sDAAkB;;;;;;;;;;;;8CAEjC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,6LAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,6LAAC;4CAAE,WAAU;sDAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOtC;gBAAC;gBAAG;gBAAG;gBAAG;gBAAG;gBAAG;gBAAG;aAAE,CAAC,GAAG,CAAC,CAAC,yBAC1B,6LAAC,8IAAA,CAAA,UAAO;oBAAgB,IAAI,CAAC,MAAM,EAAE,UAAU;oBAAE,WAAU;8BACzD,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAuC;oCAC1C;oCAAS;;;;;;;0CAEpB,6LAAC;gCAAE,WAAU;0CAAuB;;;;;;;;;;;;mBAL1B;;;;;;;;;;;AAatB;GArGwB;KAAA", "debugId": null}}]}