{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/property-plaza-pitch/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface NavigationProps {\n  currentSection: number;\n  totalSections: number;\n}\n\nexport default function Navigation({ currentSection, totalSections }: NavigationProps) {\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    const timer = setTimeout(() => setIsVisible(true), 1000);\n    return () => clearTimeout(timer);\n  }, []);\n\n  const scrollToSection = (sectionIndex: number) => {\n    const section = document.getElementById(`section-${sectionIndex}`);\n    if (section) {\n      section.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <nav className=\"fixed top-6 right-6 z-50 bg-brand-dark/80 backdrop-blur-sm rounded-full px-4 py-2 border border-brand-accent/20\">\n      <div className=\"flex items-center space-x-2\">\n        <span className=\"text-sm text-brand-text/70\">\n          {currentSection} of {totalSections}\n        </span>\n        <div className=\"flex space-x-1\">\n          {Array.from({ length: totalSections }, (_, i) => (\n            <button\n              key={i}\n              onClick={() => scrollToSection(i + 1)}\n              className={`w-2 h-2 rounded-full transition-all duration-300 ${\n                i + 1 === currentSection\n                  ? 'bg-brand-accent scale-125'\n                  : 'bg-brand-text/30 hover:bg-brand-text/50'\n              }`}\n              aria-label={`Go to section ${i + 1}`}\n            />\n          ))}\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASe,SAAS,WAAW,EAAE,cAAc,EAAE,aAAa,EAAmB;IACnF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,WAAW,IAAM,aAAa,OAAO;QACnD,OAAO,IAAM,aAAa;IAC5B,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,cAAc,CAAC,CAAC,QAAQ,EAAE,cAAc;QACjE,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;IACF;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAK,WAAU;;wBACb;wBAAe;wBAAK;;;;;;;8BAEvB,8OAAC;oBAAI,WAAU;8BACZ,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAc,GAAG,CAAC,GAAG,kBACzC,8OAAC;4BAEC,SAAS,IAAM,gBAAgB,IAAI;4BACnC,WAAW,CAAC,iDAAiD,EAC3D,IAAI,MAAM,iBACN,8BACA,2CACJ;4BACF,cAAY,CAAC,cAAc,EAAE,IAAI,GAAG;2BAP/B;;;;;;;;;;;;;;;;;;;;;AAcnB", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/property-plaza-pitch/src/components/Section.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { ReactNode } from 'react';\n\ninterface SectionProps {\n  id: string;\n  children: ReactNode;\n  className?: string;\n  background?: 'default' | 'gradient' | 'accent';\n}\n\nconst backgroundVariants = {\n  default: 'bg-brand-dark',\n  gradient: 'bg-gradient-to-br from-brand-dark via-brand-dark to-brand-dark/90',\n  accent: 'bg-gradient-to-br from-brand-dark to-brand-accent/10',\n};\n\nexport default function Section({ id, children, className = '', background = 'default' }: SectionProps) {\n  return (\n    <section\n      id={id}\n      className={`section ${backgroundVariants[background]} ${className}`}\n    >\n      <motion.div\n        initial={{ opacity: 0, y: 50 }}\n        whileInView={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.8, ease: 'easeOut' }}\n        viewport={{ once: true, amount: 0.3 }}\n        className=\"container mx-auto px-6 max-w-6xl\"\n      >\n        {children}\n      </motion.div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYA,MAAM,qBAAqB;IACzB,SAAS;IACT,UAAU;IACV,QAAQ;AACV;AAEe,SAAS,QAAQ,EAAE,EAAE,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE,aAAa,SAAS,EAAgB;IACpG,qBACE,8OAAC;QACC,IAAI;QACJ,WAAW,CAAC,QAAQ,EAAE,kBAAkB,CAAC,WAAW,CAAC,CAAC,EAAE,WAAW;kBAEnE,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,aAAa;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAChC,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAU;YAC7C,UAAU;gBAAE,MAAM;gBAAM,QAAQ;YAAI;YACpC,WAAU;sBAET;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/property-plaza-pitch/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport Navigation from '@/components/Navigation';\nimport Section from '@/components/Section';\n\nexport default function Home() {\n  const [currentSection, setCurrentSection] = useState(1);\n  const totalSections = 9;\n\n  useEffect(() => {\n    const handleScroll = () => {\n      const sections = document.querySelectorAll('.section');\n      const scrollPosition = window.scrollY + window.innerHeight / 2;\n\n      sections.forEach((section, index) => {\n        const element = section as HTMLElement;\n        const top = element.offsetTop;\n        const bottom = top + element.offsetHeight;\n\n        if (scrollPosition >= top && scrollPosition < bottom) {\n          setCurrentSection(index + 1);\n        }\n      });\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  return (\n    <main className=\"relative\">\n      <Navigation currentSection={currentSection} totalSections={totalSections} />\n\n      {/* Section 1: Hero */}\n      <Section id=\"section-1\" background=\"gradient\">\n        <div className=\"text-center\">\n          <motion.div\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 1, ease: 'easeOut' }}\n            className=\"mb-8\"\n          >\n            <h1 className=\"text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-brand-text to-brand-accent bg-clip-text text-transparent\">\n              Empower Property Decisions in Bali\n            </h1>\n            <p className=\"text-xl md:text-2xl text-brand-text/80 mb-8 max-w-3xl mx-auto\">\n              Through transparency, knowledge, connection and trust.\n            </p>\n          </motion.div>\n\n          <motion.button\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.5 }}\n            onClick={() => document.getElementById('section-2')?.scrollIntoView({ behavior: 'smooth' })}\n            className=\"group bg-brand-accent hover:bg-brand-accent/90 text-white px-8 py-4 rounded-full text-lg font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:shadow-brand-accent/25\"\n          >\n            Start the Experience\n            <motion.span\n              animate={{ y: [0, 5, 0] }}\n              transition={{ duration: 1.5, repeat: Infinity }}\n              className=\"ml-2 inline-block\"\n            >\n              ↓\n            </motion.span>\n          </motion.button>\n        </div>\n      </Section>\n\n      {/* Section 2: The Problem */}\n      <Section id=\"section-2\" background=\"default\">\n        <div className=\"text-center max-w-4xl mx-auto\">\n          <motion.h2\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"text-4xl md:text-5xl font-bold mb-12 text-brand-text\"\n          >\n            The Bali real estate market feels complex, risky, and disconnected.\n          </motion.h2>\n\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            {[\n              {\n                title: \"Legal Confusion\",\n                description: \"Buyers don't understand leasehold laws\",\n                icon: \"⚖️\"\n              },\n              {\n                title: \"Limited Reach\",\n                description: \"Sellers can't reach serious foreign buyers\",\n                icon: \"🌐\"\n              },\n              {\n                title: \"No Trust Source\",\n                description: \"No trusted, centralized source of truth\",\n                icon: \"🔍\"\n              }\n            ].map((problem, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, y: 50 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.2 }}\n                className=\"bg-brand-dark/50 border border-brand-accent/20 rounded-xl p-6 hover:border-brand-accent/40 transition-all duration-300\"\n              >\n                <div className=\"text-4xl mb-4\">{problem.icon}</div>\n                <h3 className=\"text-xl font-semibold mb-3 text-brand-accent\">{problem.title}</h3>\n                <p className=\"text-brand-text/70\">{problem.description}</p>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </Section>\n\n      {/* Placeholder sections for now */}\n      {Array.from({ length: 7 }, (_, i) => (\n        <Section key={i + 3} id={`section-${i + 3}`} background=\"default\">\n          <div className=\"text-center\">\n            <h2 className=\"text-4xl font-bold mb-4 text-brand-text\">\n              Section {i + 3} - Coming Soon\n            </h2>\n            <p className=\"text-brand-text/70\">\n              This section will be built in the next steps\n            </p>\n          </div>\n        </Section>\n      ))}\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,gBAAgB;IAEtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,MAAM,WAAW,SAAS,gBAAgB,CAAC;YAC3C,MAAM,iBAAiB,OAAO,OAAO,GAAG,OAAO,WAAW,GAAG;YAE7D,SAAS,OAAO,CAAC,CAAC,SAAS;gBACzB,MAAM,UAAU;gBAChB,MAAM,MAAM,QAAQ,SAAS;gBAC7B,MAAM,SAAS,MAAM,QAAQ,YAAY;gBAEzC,IAAI,kBAAkB,OAAO,iBAAiB,QAAQ;oBACpD,kBAAkB,QAAQ;gBAC5B;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAK,WAAU;;0BACd,8OAAC,gIAAA,CAAA,UAAU;gBAAC,gBAAgB;gBAAgB,eAAe;;;;;;0BAG3D,8OAAC,6HAAA,CAAA,UAAO;gBAAC,IAAG;gBAAY,YAAW;0BACjC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAG,MAAM;4BAAU;4BAC3C,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;8CAAqH;;;;;;8CAGnI,8OAAC;oCAAE,WAAU;8CAAgE;;;;;;;;;;;;sCAK/E,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,SAAS,IAAM,SAAS,cAAc,CAAC,cAAc,eAAe;oCAAE,UAAU;gCAAS;4BACzF,WAAU;;gCACX;8CAEC,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,SAAS;wCAAE,GAAG;4CAAC;4CAAG;4CAAG;yCAAE;oCAAC;oCACxB,YAAY;wCAAE,UAAU;wCAAK,QAAQ;oCAAS;oCAC9C,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC,6HAAA,CAAA,UAAO;gBAAC,IAAG;gBAAY,YAAW;0BACjC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;sCACX;;;;;;sCAID,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCACE,OAAO;oCACP,aAAa;oCACb,MAAM;gCACR;gCACA;oCACE,OAAO;oCACP,aAAa;oCACb,MAAM;gCACR;gCACA;oCACE,OAAO;oCACP,aAAa;oCACb,MAAM;gCACR;6BACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;sDAAiB,QAAQ,IAAI;;;;;;sDAC5C,8OAAC;4CAAG,WAAU;sDAAgD,QAAQ,KAAK;;;;;;sDAC3E,8OAAC;4CAAE,WAAU;sDAAsB,QAAQ,WAAW;;;;;;;mCARjD;;;;;;;;;;;;;;;;;;;;;YAgBd,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,CAAC,GAAG,kBAC7B,8OAAC,6HAAA,CAAA,UAAO;oBAAa,IAAI,CAAC,QAAQ,EAAE,IAAI,GAAG;oBAAE,YAAW;8BACtD,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAA0C;oCAC7C,IAAI;oCAAE;;;;;;;0CAEjB,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;mBALxB,IAAI;;;;;;;;;;;AAa1B", "debugId": null}}]}