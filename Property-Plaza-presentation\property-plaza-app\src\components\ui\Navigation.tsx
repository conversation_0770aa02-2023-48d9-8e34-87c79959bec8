'use client';

import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';

interface NavigationProps {
  currentSlide: number;
  totalSlides: number;
  onSlideChange: (slide: number) => void;
}

const sections = [
  { id: 'hero', label: 'Hero', title: 'Empower Property Decisions' },
  { id: 'problem', label: 'Problem', title: 'Market Challenges' },
  { id: 'mission', label: 'Mission', title: '4 Pillars' },
  { id: 'about', label: 'About', title: 'Property Plaza' },
  { id: 'partnership', label: 'Partnership', title: 'Paradise Indonesia' },
  { id: 'synergy', label: 'Synergy', title: 'Joint Value' },
  { id: 'pilot', label: 'Pilot', title: 'Campaign Plan' },
  { id: 'metrics', label: 'Metrics', title: 'KPIs & Tracking' },
  { id: 'cta', label: 'CTA', title: 'Let\'s Launch' }
];

export default function Navigation({ 
  currentSlide, 
  totalSlides, 
  onSlideChange 
}: NavigationProps) {
  const [isOpen, setIsOpen] = useState(false);

  const scrollToSection = (sectionId: string, slideIndex: number) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
      });
      onSlideChange(slideIndex);
      setIsOpen(false);
    }
  };

  const goToNextSlide = () => {
    if (currentSlide < totalSlides) {
      const nextSection = sections[currentSlide];
      scrollToSection(nextSection.id, currentSlide + 1);
    }
  };

  const goToPrevSlide = () => {
    if (currentSlide > 1) {
      const prevSection = sections[currentSlide - 2];
      scrollToSection(prevSection.id, currentSlide - 1);
    }
  };

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowDown' || e.key === ' ') {
        e.preventDefault();
        goToNextSlide();
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        goToPrevSlide();
      } else if (e.key === 'Escape') {
        setIsOpen(false);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [currentSlide]);

  return (
    <>
      {/* Navigation Toggle Button */}
      <motion.button
        className="fixed top-8 left-8 z-50 bg-white/10 backdrop-blur-md rounded-full p-3 hover:bg-white/20 transition-colors"
        onClick={() => setIsOpen(!isOpen)}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 1 }}
      >
        <motion.div
          className="w-6 h-6 flex flex-col justify-center items-center"
          animate={isOpen ? "open" : "closed"}
        >
          <motion.span
            className="w-5 h-0.5 bg-text-default block"
            variants={{
              closed: { rotate: 0, y: 0 },
              open: { rotate: 45, y: 2 }
            }}
          />
          <motion.span
            className="w-5 h-0.5 bg-text-default block mt-1"
            variants={{
              closed: { opacity: 1 },
              open: { opacity: 0 }
            }}
          />
          <motion.span
            className="w-5 h-0.5 bg-text-default block mt-1"
            variants={{
              closed: { rotate: 0, y: 0 },
              open: { rotate: -45, y: -2 }
            }}
          />
        </motion.div>
      </motion.button>

      {/* Navigation Menu */}
      <motion.nav
        className="fixed top-20 left-8 z-40 bg-white/95 backdrop-blur-md rounded-2xl shadow-xl overflow-hidden"
        initial={{ opacity: 0, x: -20, scale: 0.9 }}
        animate={{ 
          opacity: isOpen ? 1 : 0,
          x: isOpen ? 0 : -20,
          scale: isOpen ? 1 : 0.9,
          pointerEvents: isOpen ? 'auto' : 'none'
        }}
        transition={{ duration: 0.3, ease: "easeOut" }}
      >
        <div className="p-4 space-y-2 min-w-[280px]">
          <h3 className="text-sm font-semibold text-text-muted uppercase tracking-wide mb-4">
            Presentation Sections
          </h3>
          {sections.map((section, index) => (
            <motion.button
              key={section.id}
              className={`
                w-full text-left p-3 rounded-lg transition-all duration-200
                ${currentSlide === index + 1 
                  ? 'bg-primary text-white shadow-md' 
                  : 'hover:bg-primary/10 text-text-default'
                }
              `}
              onClick={() => scrollToSection(section.id, index + 1)}
              whileHover={{ x: currentSlide === index + 1 ? 0 : 4 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">{section.title}</div>
                  <div className={`text-sm ${
                    currentSlide === index + 1 ? 'text-white/80' : 'text-text-muted'
                  }`}>
                    Section {index + 1}
                  </div>
                </div>
                <div className={`
                  w-2 h-2 rounded-full transition-colors
                  ${currentSlide === index + 1 ? 'bg-white' : 'bg-primary/30'}
                `} />
              </div>
            </motion.button>
          ))}
        </div>
      </motion.nav>

      {/* Navigation Arrows */}
      <div className="fixed right-8 top-1/2 -translate-y-1/2 z-50 space-y-4">
        <motion.button
          className={`
            p-3 rounded-full bg-white/10 backdrop-blur-md transition-all duration-300
            ${currentSlide > 1 
              ? 'hover:bg-white/20 text-text-default' 
              : 'opacity-50 cursor-not-allowed text-text-muted'
            }
          `}
          onClick={goToPrevSlide}
          disabled={currentSlide <= 1}
          whileHover={currentSlide > 1 ? { scale: 1.05 } : {}}
          whileTap={currentSlide > 1 ? { scale: 0.95 } : {}}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 1.2 }}
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
          </svg>
        </motion.button>
        
        <motion.button
          className={`
            p-3 rounded-full bg-white/10 backdrop-blur-md transition-all duration-300
            ${currentSlide < totalSlides 
              ? 'hover:bg-white/20 text-text-default' 
              : 'opacity-50 cursor-not-allowed text-text-muted'
            }
          `}
          onClick={goToNextSlide}
          disabled={currentSlide >= totalSlides}
          whileHover={currentSlide < totalSlides ? { scale: 1.05 } : {}}
          whileTap={currentSlide < totalSlides ? { scale: 0.95 } : {}}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 1.4 }}
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </motion.button>
      </div>

      {/* Keyboard Hints */}
      <motion.div
        className="fixed bottom-8 left-8 z-50 bg-white/10 backdrop-blur-md rounded-lg px-4 py-2"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 2 }}
      >
        <div className="text-sm text-text-muted">
          <span className="font-medium">Navigation:</span> ↑↓ arrows, Space, or click
        </div>
      </motion.div>
    </>
  );
}
