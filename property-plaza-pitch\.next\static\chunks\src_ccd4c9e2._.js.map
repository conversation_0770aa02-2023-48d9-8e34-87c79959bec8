{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/property-plaza-pitch/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface NavigationProps {\n  currentSection: number;\n  totalSections: number;\n}\n\nexport default function Navigation({ currentSection, totalSections }: NavigationProps) {\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    const timer = setTimeout(() => setIsVisible(true), 1000);\n    return () => clearTimeout(timer);\n  }, []);\n\n  const scrollToSection = (sectionIndex: number) => {\n    const section = document.getElementById(`section-${sectionIndex}`);\n    if (section) {\n      section.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <nav className=\"fixed top-6 right-6 z-50 bg-brand-dark/80 backdrop-blur-sm rounded-full px-4 py-2 border border-brand-accent/20\">\n      <div className=\"flex items-center space-x-2\">\n        <span className=\"text-sm text-brand-text/70\">\n          {currentSection} of {totalSections}\n        </span>\n        <div className=\"flex space-x-1\">\n          {Array.from({ length: totalSections }, (_, i) => (\n            <button\n              key={i}\n              onClick={() => scrollToSection(i + 1)}\n              className={`w-2 h-2 rounded-full transition-all duration-300 ${\n                i + 1 === currentSection\n                  ? 'bg-brand-accent scale-125'\n                  : 'bg-brand-text/30 hover:bg-brand-text/50'\n              }`}\n              aria-label={`Go to section ${i + 1}`}\n            />\n          ))}\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AASe,SAAS,WAAW,EAAE,cAAc,EAAE,aAAa,EAAmB;;IACnF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM,QAAQ;8CAAW,IAAM,aAAa;6CAAO;YACnD;wCAAO,IAAM,aAAa;;QAC5B;+BAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,cAAc,CAAC,CAAC,QAAQ,EAAE,cAAc;QACjE,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;IACF;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAK,WAAU;;wBACb;wBAAe;wBAAK;;;;;;;8BAEvB,6LAAC;oBAAI,WAAU;8BACZ,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAc,GAAG,CAAC,GAAG,kBACzC,6LAAC;4BAEC,SAAS,IAAM,gBAAgB,IAAI;4BACnC,WAAW,CAAC,iDAAiD,EAC3D,IAAI,MAAM,iBACN,8BACA,2CACJ;4BACF,cAAY,CAAC,cAAc,EAAE,IAAI,GAAG;2BAP/B;;;;;;;;;;;;;;;;;;;;;AAcnB;GAxCwB;KAAA", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/property-plaza-pitch/src/components/Section.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { ReactNode } from 'react';\n\ninterface SectionProps {\n  id: string;\n  children: ReactNode;\n  className?: string;\n  background?: 'default' | 'gradient' | 'accent';\n}\n\nconst backgroundVariants = {\n  default: 'bg-brand-dark',\n  gradient: 'bg-gradient-to-br from-brand-dark via-brand-dark to-brand-dark/90',\n  accent: 'bg-gradient-to-br from-brand-dark to-brand-accent/10',\n};\n\nexport default function Section({ id, children, className = '', background = 'default' }: SectionProps) {\n  return (\n    <section\n      id={id}\n      className={`section ${backgroundVariants[background]} ${className}`}\n    >\n      <motion.div\n        initial={{ opacity: 0, y: 50 }}\n        whileInView={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.8, ease: 'easeOut' }}\n        viewport={{ once: true, amount: 0.3 }}\n        className=\"container mx-auto px-6 max-w-6xl\"\n      >\n        {children}\n      </motion.div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYA,MAAM,qBAAqB;IACzB,SAAS;IACT,UAAU;IACV,QAAQ;AACV;AAEe,SAAS,QAAQ,EAAE,EAAE,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE,aAAa,SAAS,EAAgB;IACpG,qBACE,6LAAC;QACC,IAAI;QACJ,WAAW,CAAC,QAAQ,EAAE,kBAAkB,CAAC,WAAW,CAAC,CAAC,EAAE,WAAW;kBAEnE,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,aAAa;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAChC,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAU;YAC7C,UAAU;gBAAE,MAAM;gBAAM,QAAQ;YAAI;YACpC,WAAU;sBAET;;;;;;;;;;;AAIT;KAjBwB", "debugId": null}}, {"offset": {"line": 157, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/property-plaza-pitch/src/components/PillarCard.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\n\ninterface PillarCardProps {\n  icon: string;\n  title: string;\n  description: string;\n  index: number;\n}\n\nexport default function PillarCard({ icon, title, description, index }: PillarCardProps) {\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 50, scale: 0.9 }}\n      whileInView={{ opacity: 1, y: 0, scale: 1 }}\n      transition={{ \n        duration: 0.6, \n        delay: index * 0.15,\n        ease: 'easeOut'\n      }}\n      whileHover={{ \n        scale: 1.05,\n        y: -10,\n        transition: { duration: 0.3 }\n      }}\n      className=\"group relative bg-brand-secondary/30 border border-brand-accent/10 rounded-2xl p-10 hover:border-brand-accent/30 transition-all duration-500 backdrop-blur-sm\"\n    >\n      {/* Glow effect on hover */}\n      <div className=\"absolute inset-0 rounded-2xl bg-gradient-to-br from-brand-accent/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500\" />\n\n      <div className=\"relative z-10\">\n        {/* Icon Container */}\n        <div className=\"w-20 h-20 bg-brand-accent/20 rounded-full flex items-center justify-center mb-8 mx-auto group-hover:bg-brand-accent/30 transition-colors duration-300\">\n          <div className=\"w-10 h-10 bg-brand-accent rounded-full\"></div>\n        </div>\n\n        {/* Title */}\n        <h3 className=\"text-2xl font-bold mb-6 text-brand-accent text-center group-hover:text-brand-accent transition-colors duration-300\">\n          {title}\n        </h3>\n\n        {/* Description */}\n        <p className=\"text-brand-text/60 text-center leading-relaxed group-hover:text-brand-text/80 transition-colors duration-300\">\n          {description}\n        </p>\n\n        {/* Decorative line */}\n        <motion.div\n          initial={{ width: 0 }}\n          whileInView={{ width: '60%' }}\n          transition={{ duration: 0.8, delay: index * 0.15 + 0.3 }}\n          className=\"h-0.5 bg-brand-accent mt-8 mx-auto\"\n        />\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAWe,SAAS,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAmB;IACrF,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;YAAI,OAAO;QAAI;QACzC,aAAa;YAAE,SAAS;YAAG,GAAG;YAAG,OAAO;QAAE;QAC1C,YAAY;YACV,UAAU;YACV,OAAO,QAAQ;YACf,MAAM;QACR;QACA,YAAY;YACV,OAAO;YACP,GAAG,CAAC;YACJ,YAAY;gBAAE,UAAU;YAAI;QAC9B;QACA,WAAU;;0BAGV,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;;kCAIjB,6LAAC;wBAAG,WAAU;kCACX;;;;;;kCAIH,6LAAC;wBAAE,WAAU;kCACV;;;;;;kCAIH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,OAAO;wBAAE;wBACpB,aAAa;4BAAE,OAAO;wBAAM;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO,QAAQ,OAAO;wBAAI;wBACvD,WAAU;;;;;;;;;;;;;;;;;;AAKpB;KA9CwB", "debugId": null}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/property-plaza-pitch/src/components/BackgroundAnimation.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\n\nexport default function BackgroundAnimation() {\n  return (\n    <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n      {/* Floating particles */}\n      {Array.from({ length: 20 }, (_, i) => (\n        <motion.div\n          key={i}\n          className=\"absolute w-2 h-2 bg-brand-accent/20 rounded-full\"\n          initial={{\n            x: Math.random() * window.innerWidth,\n            y: Math.random() * window.innerHeight,\n          }}\n          animate={{\n            x: Math.random() * window.innerWidth,\n            y: Math.random() * window.innerHeight,\n          }}\n          transition={{\n            duration: Math.random() * 20 + 10,\n            repeat: Infinity,\n            repeatType: 'reverse',\n            ease: 'linear',\n          }}\n        />\n      ))}\n      \n      {/* Gradient orbs */}\n      <motion.div\n        className=\"absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-brand-accent/10 to-transparent rounded-full blur-3xl\"\n        animate={{\n          scale: [1, 1.2, 1],\n          opacity: [0.3, 0.6, 0.3],\n        }}\n        transition={{\n          duration: 8,\n          repeat: Infinity,\n          ease: 'easeInOut',\n        }}\n      />\n      \n      <motion.div\n        className=\"absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-l from-brand-accent/15 to-transparent rounded-full blur-3xl\"\n        animate={{\n          scale: [1.2, 1, 1.2],\n          opacity: [0.4, 0.2, 0.4],\n        }}\n        transition={{\n          duration: 10,\n          repeat: Infinity,\n          ease: 'easeInOut',\n        }}\n      />\n      \n      {/* Subtle grid pattern */}\n      <div className=\"absolute inset-0 opacity-5\">\n        <div className=\"w-full h-full\" style={{\n          backgroundImage: `\n            linear-gradient(rgba(16, 185, 129, 0.1) 1px, transparent 1px),\n            linear-gradient(90deg, rgba(16, 185, 129, 0.1) 1px, transparent 1px)\n          `,\n          backgroundSize: '50px 50px'\n        }} />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;YAEZ,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAG,GAAG,CAAC,GAAG,kBAC9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAU;oBACV,SAAS;wBACP,GAAG,KAAK,MAAM,KAAK,OAAO,UAAU;wBACpC,GAAG,KAAK,MAAM,KAAK,OAAO,WAAW;oBACvC;oBACA,SAAS;wBACP,GAAG,KAAK,MAAM,KAAK,OAAO,UAAU;wBACpC,GAAG,KAAK,MAAM,KAAK,OAAO,WAAW;oBACvC;oBACA,YAAY;wBACV,UAAU,KAAK,MAAM,KAAK,KAAK;wBAC/B,QAAQ;wBACR,YAAY;wBACZ,MAAM;oBACR;mBAfK;;;;;0BAoBT,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;oBAClB,SAAS;wBAAC;wBAAK;wBAAK;qBAAI;gBAC1B;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;;;;;;0BAGF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,OAAO;wBAAC;wBAAK;wBAAG;qBAAI;oBACpB,SAAS;wBAAC;wBAAK;wBAAK;qBAAI;gBAC1B;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;;;;;;0BAIF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;oBAAgB,OAAO;wBACpC,iBAAiB,CAAC;;;UAGlB,CAAC;wBACD,gBAAgB;oBAClB;;;;;;;;;;;;;;;;;AAIR;KAhEwB", "debugId": null}}, {"offset": {"line": 396, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/property-plaza-pitch/src/components/StatCard.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useEffect, useState } from 'react';\n\ninterface StatCardProps {\n  number: string;\n  label: string;\n  description: string;\n  index: number;\n  icon?: string;\n}\n\nexport default function StatCard({ number, label, description, index, icon }: StatCardProps) {\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    const timer = setTimeout(() => setIsVisible(true), index * 200);\n    return () => clearTimeout(timer);\n  }, [index]);\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 50, scale: 0.8 }}\n      whileInView={{ opacity: 1, y: 0, scale: 1 }}\n      transition={{ \n        duration: 0.6, \n        delay: index * 0.1,\n        ease: 'easeOut'\n      }}\n      whileHover={{ \n        scale: 1.05,\n        y: -5,\n        transition: { duration: 0.3 }\n      }}\n      className=\"group relative bg-brand-secondary/30 border border-brand-accent/10 rounded-2xl p-8 hover:border-brand-accent/30 transition-all duration-500 backdrop-blur-sm\"\n    >\n      {/* Glow effect */}\n      <div className=\"absolute inset-0 rounded-2xl bg-gradient-to-br from-brand-accent/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500\" />\n\n      <div className=\"relative z-10 text-center\">\n        {/* Icon */}\n        {icon && (\n          <div className=\"w-16 h-16 bg-brand-accent/20 rounded-full flex items-center justify-center mb-6 mx-auto group-hover:bg-brand-accent/30 transition-colors duration-300\">\n            <div className=\"w-8 h-8 bg-brand-accent rounded-full\"></div>\n          </div>\n        )}\n\n        {/* Number with counting animation */}\n        <motion.div\n          initial={{ scale: 0.5, opacity: 0 }}\n          whileInView={{ scale: 1, opacity: 1 }}\n          transition={{ duration: 0.8, delay: index * 0.1 + 0.3 }}\n          className=\"text-4xl md:text-5xl font-bold text-brand-accent mb-4\"\n        >\n          {isVisible && (\n            <CountingNumber\n              target={parseInt(number.replace(/\\D/g, ''))}\n              suffix={number.replace(/\\d/g, '')}\n            />\n          )}\n        </motion.div>\n\n        {/* Label */}\n        <motion.h3\n          initial={{ opacity: 0 }}\n          whileInView={{ opacity: 1 }}\n          transition={{ duration: 0.6, delay: index * 0.1 + 0.4 }}\n          className=\"text-lg font-semibold text-brand-text mb-3\"\n        >\n          {label}\n        </motion.h3>\n\n        {/* Description */}\n        <motion.p\n          initial={{ opacity: 0 }}\n          whileInView={{ opacity: 1 }}\n          transition={{ duration: 0.6, delay: index * 0.1 + 0.5 }}\n          className=\"text-sm text-brand-text/60 leading-relaxed\"\n        >\n          {description}\n        </motion.p>\n      </div>\n    </motion.div>\n  );\n}\n\n// Helper component for counting animation\nfunction CountingNumber({ target, suffix }: { target: number; suffix: string }) {\n  const [count, setCount] = useState(0);\n\n  useEffect(() => {\n    const duration = 2000; // 2 seconds\n    const steps = 60;\n    const increment = target / steps;\n    let current = 0;\n\n    const timer = setInterval(() => {\n      current += increment;\n      if (current >= target) {\n        setCount(target);\n        clearInterval(timer);\n      } else {\n        setCount(Math.floor(current));\n      }\n    }, duration / steps);\n\n    return () => clearInterval(timer);\n  }, [target]);\n\n  return <span>{count}{suffix}</span>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAae,SAAS,SAAS,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,IAAI,EAAiB;;IACzF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM,QAAQ;4CAAW,IAAM,aAAa;2CAAO,QAAQ;YAC3D;sCAAO,IAAM,aAAa;;QAC5B;6BAAG;QAAC;KAAM;IAEV,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;YAAI,OAAO;QAAI;QACzC,aAAa;YAAE,SAAS;YAAG,GAAG;YAAG,OAAO;QAAE;QAC1C,YAAY;YACV,UAAU;YACV,OAAO,QAAQ;YACf,MAAM;QACR;QACA,YAAY;YACV,OAAO;YACP,GAAG,CAAC;YACJ,YAAY;gBAAE,UAAU;YAAI;QAC9B;QACA,WAAU;;0BAGV,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC;gBAAI,WAAU;;oBAEZ,sBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;;kCAKnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,OAAO;4BAAK,SAAS;wBAAE;wBAClC,aAAa;4BAAE,OAAO;4BAAG,SAAS;wBAAE;wBACpC,YAAY;4BAAE,UAAU;4BAAK,OAAO,QAAQ,MAAM;wBAAI;wBACtD,WAAU;kCAET,2BACC,6LAAC;4BACC,QAAQ,SAAS,OAAO,OAAO,CAAC,OAAO;4BACvC,QAAQ,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;kCAMpC,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wBACR,SAAS;4BAAE,SAAS;wBAAE;wBACtB,aAAa;4BAAE,SAAS;wBAAE;wBAC1B,YAAY;4BAAE,UAAU;4BAAK,OAAO,QAAQ,MAAM;wBAAI;wBACtD,WAAU;kCAET;;;;;;kCAIH,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,SAAS;4BAAE,SAAS;wBAAE;wBACtB,aAAa;4BAAE,SAAS;wBAAE;wBAC1B,YAAY;4BAAE,UAAU;4BAAK,OAAO,QAAQ,MAAM;wBAAI;wBACtD,WAAU;kCAET;;;;;;;;;;;;;;;;;;AAKX;GAxEwB;KAAA;AA0ExB,0CAA0C;AAC1C,SAAS,eAAe,EAAE,MAAM,EAAE,MAAM,EAAsC;;IAC5E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,WAAW,MAAM,YAAY;YACnC,MAAM,QAAQ;YACd,MAAM,YAAY,SAAS;YAC3B,IAAI,UAAU;YAEd,MAAM,QAAQ;kDAAY;oBACxB,WAAW;oBACX,IAAI,WAAW,QAAQ;wBACrB,SAAS;wBACT,cAAc;oBAChB,OAAO;wBACL,SAAS,KAAK,KAAK,CAAC;oBACtB;gBACF;iDAAG,WAAW;YAEd;4CAAO,IAAM,cAAc;;QAC7B;mCAAG;QAAC;KAAO;IAEX,qBAAO,6LAAC;;YAAM;YAAO;;;;;;;AACvB;IAvBS;MAAA", "debugId": null}}, {"offset": {"line": 602, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/property-plaza-pitch/src/components/LogoMerge.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\n\nexport default function LogoMerge() {\n  return (\n    <div className=\"relative flex items-center justify-center space-x-8 py-16\">\n      {/* Property Plaza Logo */}\n      <motion.div\n        initial={{ x: -100, opacity: 0 }}\n        whileInView={{ x: 0, opacity: 1 }}\n        transition={{ duration: 1, ease: 'easeOut' }}\n        className=\"relative\"\n      >\n        <div className=\"w-24 h-24 bg-gradient-to-br from-brand-accent to-brand-accent/80 rounded-2xl flex items-center justify-center shadow-lg\">\n          <span className=\"text-white font-bold text-xl\">PP</span>\n        </div>\n        <motion.div\n          initial={{ opacity: 0, y: 10 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.5 }}\n          className=\"absolute -bottom-8 left-1/2 transform -translate-x-1/2 whitespace-nowrap\"\n        >\n          <span className=\"text-sm font-medium text-brand-text\">Property Plaza</span>\n        </motion.div>\n      </motion.div>\n\n      {/* Merge Animation */}\n      <motion.div\n        initial={{ scale: 0, opacity: 0 }}\n        whileInView={{ scale: 1, opacity: 1 }}\n        transition={{ duration: 0.8, delay: 0.8 }}\n        className=\"relative\"\n      >\n        <motion.div\n          animate={{ \n            rotate: 360,\n            scale: [1, 1.2, 1]\n          }}\n          transition={{ \n            duration: 3,\n            repeat: Infinity,\n            ease: 'linear'\n          }}\n          className=\"text-4xl text-brand-accent\"\n        >\n          ⚡\n        </motion.div>\n        \n        {/* Connecting lines */}\n        <motion.div\n          initial={{ width: 0 }}\n          whileInView={{ width: '100%' }}\n          transition={{ duration: 1, delay: 1 }}\n          className=\"absolute top-1/2 left-0 h-0.5 bg-gradient-to-r from-brand-accent/50 to-transparent transform -translate-y-1/2 -translate-x-full w-16\"\n        />\n        <motion.div\n          initial={{ width: 0 }}\n          whileInView={{ width: '100%' }}\n          transition={{ duration: 1, delay: 1 }}\n          className=\"absolute top-1/2 right-0 h-0.5 bg-gradient-to-l from-brand-accent/50 to-transparent transform -translate-y-1/2 translate-x-full w-16\"\n        />\n      </motion.div>\n\n      {/* Paradise Indonesia Logo */}\n      <motion.div\n        initial={{ x: 100, opacity: 0 }}\n        whileInView={{ x: 0, opacity: 1 }}\n        transition={{ duration: 1, ease: 'easeOut' }}\n        className=\"relative\"\n      >\n        <div className=\"w-24 h-24 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg\">\n          <span className=\"text-white font-bold text-xl\">PI</span>\n        </div>\n        <motion.div\n          initial={{ opacity: 0, y: 10 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.5 }}\n          className=\"absolute -bottom-8 left-1/2 transform -translate-x-1/2 whitespace-nowrap\"\n        >\n          <span className=\"text-sm font-medium text-brand-text\">Paradise Indonesia</span>\n        </motion.div>\n      </motion.div>\n\n      {/* Merged Result */}\n      <motion.div\n        initial={{ scale: 0, opacity: 0 }}\n        whileInView={{ scale: 1, opacity: 1 }}\n        transition={{ duration: 1, delay: 1.5 }}\n        className=\"absolute\"\n      >\n        <motion.div\n          animate={{ \n            boxShadow: [\n              '0 0 20px rgba(16, 185, 129, 0.3)',\n              '0 0 40px rgba(16, 185, 129, 0.5)',\n              '0 0 20px rgba(16, 185, 129, 0.3)'\n            ]\n          }}\n          transition={{ duration: 2, repeat: Infinity }}\n          className=\"w-32 h-32 bg-gradient-to-br from-brand-accent via-blue-500 to-brand-accent rounded-3xl flex items-center justify-center\"\n        >\n          <div className=\"text-center\">\n            <div className=\"text-white font-bold text-lg\">PP × PI</div>\n            <div className=\"text-white/80 text-xs mt-1\">Partnership</div>\n          </div>\n        </motion.div>\n      </motion.div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,GAAG,CAAC;oBAAK,SAAS;gBAAE;gBAC/B,aAAa;oBAAE,GAAG;oBAAG,SAAS;gBAAE;gBAChC,YAAY;oBAAE,UAAU;oBAAG,MAAM;gBAAU;gBAC3C,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAA+B;;;;;;;;;;;kCAEjD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;kCAEV,cAAA,6LAAC;4BAAK,WAAU;sCAAsC;;;;;;;;;;;;;;;;;0BAK1D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBAChC,aAAa;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBACpC,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;gBACxC,WAAU;;kCAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BACP,QAAQ;4BACR,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;wBACpB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;wBACA,WAAU;kCACX;;;;;;kCAKD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,OAAO;wBAAE;wBACpB,aAAa;4BAAE,OAAO;wBAAO;wBAC7B,YAAY;4BAAE,UAAU;4BAAG,OAAO;wBAAE;wBACpC,WAAU;;;;;;kCAEZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,OAAO;wBAAE;wBACpB,aAAa;4BAAE,OAAO;wBAAO;wBAC7B,YAAY;4BAAE,UAAU;4BAAG,OAAO;wBAAE;wBACpC,WAAU;;;;;;;;;;;;0BAKd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,GAAG;oBAAK,SAAS;gBAAE;gBAC9B,aAAa;oBAAE,GAAG;oBAAG,SAAS;gBAAE;gBAChC,YAAY;oBAAE,UAAU;oBAAG,MAAM;gBAAU;gBAC3C,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAA+B;;;;;;;;;;;kCAEjD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;kCAEV,cAAA,6LAAC;4BAAK,WAAU;sCAAsC;;;;;;;;;;;;;;;;;0BAK1D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBAChC,aAAa;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBACpC,YAAY;oBAAE,UAAU;oBAAG,OAAO;gBAAI;gBACtC,WAAU;0BAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBACP,WAAW;4BACT;4BACA;4BACA;yBACD;oBACH;oBACA,YAAY;wBAAE,UAAU;wBAAG,QAAQ;oBAAS;oBAC5C,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAA+B;;;;;;0CAC9C,6LAAC;gCAAI,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxD;KA1GwB", "debugId": null}}, {"offset": {"line": 897, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/property-plaza-pitch/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport Navigation from '@/components/Navigation';\nimport Section from '@/components/Section';\nimport PillarCard from '@/components/PillarCard';\nimport BackgroundAnimation from '@/components/BackgroundAnimation';\nimport StatCard from '@/components/StatCard';\nimport LogoMerge from '@/components/LogoMerge';\n\nexport default function Home() {\n  const [currentSection, setCurrentSection] = useState(1);\n  const totalSections = 9;\n\n  useEffect(() => {\n    const handleScroll = () => {\n      const sections = document.querySelectorAll('.section');\n      const scrollPosition = window.scrollY + window.innerHeight / 2;\n\n      sections.forEach((section, index) => {\n        const element = section as HTMLElement;\n        const top = element.offsetTop;\n        const bottom = top + element.offsetHeight;\n\n        if (scrollPosition >= top && scrollPosition < bottom) {\n          setCurrentSection(index + 1);\n        }\n      });\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  return (\n    <main className=\"relative\">\n      <Navigation currentSection={currentSection} totalSections={totalSections} />\n\n      {/* Section 1: Hero */}\n      <Section id=\"section-1\" background=\"gradient\">\n        <BackgroundAnimation />\n        <div className=\"text-center relative z-10\">\n          <motion.div\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 1, ease: 'easeOut' }}\n            className=\"mb-8\"\n          >\n            <motion.h1\n              className=\"text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-brand-text to-brand-accent bg-clip-text text-transparent\"\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 1.2, delay: 0.2 }}\n            >\n              Empower Property Decisions in Bali\n            </motion.h1>\n            <motion.p\n              className=\"text-xl md:text-2xl text-brand-text/70 mb-8 max-w-3xl mx-auto\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 1, delay: 0.4 }}\n            >\n              Through transparency, knowledge, connection and trust.\n            </motion.p>\n          </motion.div>\n\n          <motion.button\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.6 }}\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n            onClick={() => document.getElementById('section-2')?.scrollIntoView({ behavior: 'smooth' })}\n            className=\"group relative bg-brand-accent hover:bg-brand-accent/90 text-white px-8 py-4 rounded-full text-lg font-semibold transition-all duration-300 transform hover:shadow-lg hover:shadow-brand-accent/25 overflow-hidden\"\n          >\n            <span className=\"relative z-10\">\n              Start the Experience\n              <motion.span\n                animate={{ y: [0, 5, 0] }}\n                transition={{ duration: 1.5, repeat: Infinity }}\n                className=\"ml-2 inline-block\"\n              >\n                ↓\n              </motion.span>\n            </span>\n            <motion.div\n              className=\"absolute inset-0 bg-gradient-to-r from-brand-accent to-brand-accent/80\"\n              whileHover={{ scale: 1.1 }}\n              transition={{ duration: 0.3 }}\n            />\n          </motion.button>\n        </div>\n      </Section>\n\n      {/* Section 2: The Problem */}\n      <Section id=\"section-2\" background=\"default\">\n        <div className=\"text-center max-w-5xl mx-auto\">\n          <motion.h2\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"text-4xl md:text-5xl font-bold mb-4 text-brand-text\"\n          >\n            The Bali real estate market feels\n          </motion.h2>\n          <motion.div\n            initial={{ opacity: 0, scale: 0.8 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            className=\"mb-16\"\n          >\n            <span className=\"text-4xl md:text-5xl font-bold text-red-400\">\n              complex, risky, and disconnected.\n            </span>\n          </motion.div>\n\n          <div className=\"grid md:grid-cols-3 gap-10\">\n            {[\n              {\n                title: \"Legal Confusion\",\n                description: \"Buyers don't understand leasehold laws\",\n                color: \"from-red-500/10 to-red-600/5\"\n              },\n              {\n                title: \"Limited Reach\",\n                description: \"Sellers can't reach serious foreign buyers\",\n                color: \"from-orange-500/10 to-orange-600/5\"\n              },\n              {\n                title: \"No Trust Source\",\n                description: \"No trusted, centralized source of truth\",\n                color: \"from-yellow-500/10 to-yellow-600/5\"\n              }\n            ].map((problem, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, y: 50, rotateX: -15 }}\n                whileInView={{ opacity: 1, y: 0, rotateX: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.2 }}\n                whileHover={{\n                  y: -10,\n                  scale: 1.02,\n                  transition: { duration: 0.3 }\n                }}\n                className={`relative bg-gradient-to-br ${problem.color} backdrop-blur-sm border border-brand-accent/10 rounded-xl p-10 hover:border-brand-accent/30 transition-all duration-300 group`}\n              >\n                <div className=\"absolute inset-0 bg-brand-secondary/30 rounded-xl\" />\n                <div className=\"relative z-10\">\n                  <div className=\"w-16 h-16 bg-brand-accent/20 rounded-full flex items-center justify-center mb-8 mx-auto\">\n                    <div className=\"w-8 h-8 bg-brand-accent rounded-full\"></div>\n                  </div>\n                  <h3 className=\"text-xl font-semibold mb-6 text-brand-accent group-hover:text-brand-accent transition-colors duration-300 text-center\">\n                    {problem.title}\n                  </h3>\n                  <p className=\"text-brand-text/60 group-hover:text-brand-text/80 transition-colors duration-300 leading-relaxed text-center\">\n                    {problem.description}\n                  </p>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </Section>\n\n      {/* Section 3: The 4 Pillars of Empowerment */}\n      <Section id=\"section-3\" background=\"accent\">\n        <div className=\"text-center max-w-6xl mx-auto\">\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"mb-16\"\n          >\n            <h2 className=\"text-4xl md:text-5xl font-bold mb-6 text-brand-text\">\n              Our Mission: The 4 Pillars of Empowerment\n            </h2>\n            <p className=\"text-xl text-brand-text/70 max-w-3xl mx-auto\">\n              We believe in transforming the Bali real estate market through four fundamental principles\n            </p>\n          </motion.div>\n\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            <PillarCard\n              icon=\"🔍\"\n              title=\"Transparency\"\n              description=\"Open, honest, no surprises — especially about contracts, prices, and regulations.\"\n              index={0}\n            />\n            <PillarCard\n              icon=\"📚\"\n              title=\"Knowledge\"\n              description=\"Clear guides and content in your language, so you can actually understand the rules.\"\n              index={1}\n            />\n            <PillarCard\n              icon=\"🔗\"\n              title=\"Connecting\"\n              description=\"Buyers meet real sellers, sellers meet qualified leads, experts are one click away.\"\n              index={2}\n            />\n            <PillarCard\n              icon=\"💪\"\n              title=\"Empowerment\"\n              description=\"With clarity, comes confidence. You feel ready to act — not just scroll.\"\n              index={3}\n            />\n          </div>\n        </div>\n      </Section>\n\n      {/* Section 4: Who We Are - Property Plaza */}\n      <Section id=\"section-4\" background=\"default\">\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"text-center mb-16\">\n            <motion.h2\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8 }}\n              className=\"text-4xl md:text-5xl font-bold mb-6 text-brand-text\"\n            >\n              Who We Are – Property Plaza\n            </motion.h2>\n            <motion.p\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.2 }}\n              className=\"text-xl text-brand-text/70 max-w-3xl mx-auto\"\n            >\n              A fresh approach to Bali real estate, built on trust and transparency\n            </motion.p>\n          </div>\n\n          {/* Statistics Grid */}\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16\">\n            <StatCard\n              number=\"30+\"\n              label=\"Active Listings\"\n              description=\"Carefully curated properties across Bali\"\n              index={0}\n              icon=\"🏡\"\n            />\n            <StatCard\n              number=\"350+\"\n              label=\"Early Users\"\n              description=\"Growing community of buyers and sellers\"\n              index={1}\n              icon=\"👥\"\n            />\n            <StatCard\n              number=\"30\"\n              label=\"Days Live\"\n              description=\"Fresh platform with proven traction\"\n              index={2}\n              icon=\"🚀\"\n            />\n            <StatCard\n              number=\"3\"\n              label=\"Languages\"\n              description=\"Multilingual, content-focused approach\"\n              index={3}\n              icon=\"🌍\"\n            />\n          </div>\n\n          {/* Key Features */}\n          <div className=\"grid md:grid-cols-2 gap-12 items-center\">\n            <motion.div\n              initial={{ opacity: 0, x: -50 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n            >\n              <h3 className=\"text-2xl font-bold text-brand-accent mb-6\">\n                What Makes Us Different\n              </h3>\n              <div className=\"space-y-4\">\n                {[\n                  \"Multilingual content in your language\",\n                  \"Smart AI-powered follow-up system\",\n                  \"Transparent pricing and legal guidance\",\n                  \"Direct connection between buyers and sellers\"\n                ].map((feature, index) => (\n                  <motion.div\n                    key={index}\n                    initial={{ opacity: 0, x: -20 }}\n                    whileInView={{ opacity: 1, x: 0 }}\n                    transition={{ duration: 0.6, delay: index * 0.1 }}\n                    className=\"flex items-center space-x-3\"\n                  >\n                    <div className=\"w-2 h-2 bg-brand-accent rounded-full\" />\n                    <span className=\"text-brand-text/70\">{feature}</span>\n                  </motion.div>\n                ))}\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, x: 50 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n              className=\"relative\"\n            >\n              <div className=\"bg-gradient-to-br from-brand-accent/10 to-brand-accent/5 rounded-2xl p-8 border border-brand-accent/20\">\n                <div className=\"text-center\">\n                  <div className=\"text-6xl mb-4\">📱</div>\n                  <h4 className=\"text-xl font-semibold text-brand-text mb-2\">\n                    Modern Platform\n                  </h4>\n                  <p className=\"text-brand-text/60\">\n                    Built with the latest technology for the best user experience\n                  </p>\n                </div>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      </Section>\n\n      {/* Section 5: Why Paradise Indonesia */}\n      <Section id=\"section-5\" background=\"accent\">\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"text-center mb-20\">\n            <motion.h2\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8 }}\n              className=\"text-4xl md:text-5xl font-bold mb-8 text-brand-text\"\n            >\n              Why Paradise Indonesia\n            </motion.h2>\n            <motion.p\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.2 }}\n              className=\"text-xl text-brand-text/70 max-w-3xl mx-auto\"\n            >\n              The perfect partner to bring trust and expertise to Bali real estate\n            </motion.p>\n          </div>\n\n          <div className=\"grid lg:grid-cols-2 gap-20 items-start\">\n            {/* Left side - Strengths */}\n            <motion.div\n              initial={{ opacity: 0, x: -50 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n            >\n              <h3 className=\"text-2xl font-bold text-brand-accent mb-12\">\n                Your Strengths\n              </h3>\n\n              <div className=\"space-y-10\">\n                {[\n                  {\n                    title: \"Legal Knowledge\",\n                    description: \"Deep understanding of Indonesian property law and regulations\"\n                  },\n                  {\n                    title: \"Ground Presence in Bali\",\n                    description: \"Local expertise and established network across the island\"\n                  },\n                  {\n                    title: \"Existing Trust\",\n                    description: \"Proven relationships with sellers, developers, and partners\"\n                  }\n                ].map((strength, index) => (\n                  <motion.div\n                    key={index}\n                    initial={{ opacity: 0, y: 30 }}\n                    whileInView={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.6, delay: index * 0.2 }}\n                    className=\"flex items-start space-x-6 p-6 rounded-xl bg-brand-dark/20 border border-brand-accent/10 hover:border-brand-accent/30 transition-all duration-300\"\n                  >\n                    <div className=\"flex-shrink-0 w-12 h-12 bg-brand-secondary rounded-lg flex items-center justify-center\">\n                      <div className=\"w-6 h-6 bg-brand-accent rounded-full\"></div>\n                    </div>\n                    <div className=\"flex-1\">\n                      <h4 className=\"text-xl font-semibold text-brand-text mb-3\">\n                        {strength.title}\n                      </h4>\n                      <p className=\"text-brand-text/60 leading-relaxed\">\n                        {strength.description}\n                      </p>\n                    </div>\n                  </motion.div>\n                ))}\n              </div>\n            </motion.div>\n\n            {/* Right side - Quote and Visual */}\n            <motion.div\n              initial={{ opacity: 0, x: 50 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n              className=\"relative\"\n            >\n              {/* Quote Card */}\n              <div className=\"relative bg-brand-secondary/50 rounded-3xl p-10 border border-brand-accent/20 backdrop-blur-sm\">\n                <div className=\"text-center mb-8\">\n                  <div className=\"inline-flex items-center justify-center w-20 h-20 bg-brand-accent rounded-full mb-6\">\n                    <span className=\"text-2xl font-bold text-white\">PP</span>\n                  </div>\n                  <h4 className=\"text-xl font-semibold text-brand-accent mb-3\">Property Plaza</h4>\n                  <p className=\"text-sm text-brand-text/50\">Partnership Vision</p>\n                </div>\n\n                <div className=\"text-center\">\n                  <blockquote className=\"text-xl font-medium text-brand-text mb-8 italic leading-relaxed\">\n                    \"You bring the reputation. We bring the distribution.\"\n                  </blockquote>\n\n                  <div className=\"flex items-center justify-center space-x-8 text-sm text-brand-text/60\">\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"w-8 h-8 bg-brand-dark/50 rounded-full flex items-center justify-center\">\n                        <div className=\"w-3 h-3 bg-brand-accent rounded-full\"></div>\n                      </div>\n                      <span>Local Expertise</span>\n                    </div>\n                    <span className=\"text-brand-accent font-bold\">+</span>\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"w-8 h-8 bg-brand-dark/50 rounded-full flex items-center justify-center\">\n                        <div className=\"w-3 h-3 bg-brand-accent rounded-full\"></div>\n                      </div>\n                      <span>Global Reach</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      </Section>\n\n      {/* Section 6: Together We're Stronger */}\n      <Section id=\"section-6\" background=\"default\">\n        <div className=\"max-w-6xl mx-auto text-center\">\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"mb-8\"\n          >\n            <h2 className=\"text-4xl md:text-5xl font-bold mb-6 text-brand-text\">\n              Together We're Stronger\n            </h2>\n            <p className=\"text-xl text-brand-text/80 max-w-3xl mx-auto mb-8\">\n              This isn't just promotion. It's infrastructure.\n            </p>\n          </motion.div>\n\n          {/* Logo Merge Animation */}\n          <LogoMerge />\n\n          {/* Key Message */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.5 }}\n            className=\"mt-16\"\n          >\n            <div className=\"bg-gradient-to-br from-brand-accent/10 to-brand-accent/5 rounded-3xl p-12 border border-brand-accent/20\">\n              <blockquote className=\"text-3xl md:text-4xl font-bold text-brand-text mb-8 italic\">\n                \"Together, we become the go-to destination for trusted real estate in Bali.\"\n              </blockquote>\n\n              <div className=\"grid md:grid-cols-2 gap-8 mt-12\">\n                <motion.div\n                  initial={{ opacity: 0, x: -30 }}\n                  whileInView={{ opacity: 1, x: 0 }}\n                  transition={{ duration: 0.6, delay: 0.2 }}\n                  className=\"text-left\"\n                >\n                  <h3 className=\"text-xl font-semibold text-brand-accent mb-4\">\n                    What You Bring\n                  </h3>\n                  <ul className=\"space-y-2 text-brand-text/80\">\n                    <li>• Local market expertise</li>\n                    <li>• Legal knowledge & compliance</li>\n                    <li>• Established seller relationships</li>\n                    <li>• Ground presence in Bali</li>\n                  </ul>\n                </motion.div>\n\n                <motion.div\n                  initial={{ opacity: 0, x: 30 }}\n                  whileInView={{ opacity: 1, x: 0 }}\n                  transition={{ duration: 0.6, delay: 0.4 }}\n                  className=\"text-left\"\n                >\n                  <h3 className=\"text-xl font-semibold text-brand-accent mb-4\">\n                    What We Bring\n                  </h3>\n                  <ul className=\"space-y-2 text-brand-text/80\">\n                    <li>• Global digital reach</li>\n                    <li>• Modern platform technology</li>\n                    <li>• Multilingual content strategy</li>\n                    <li>• AI-powered lead management</li>\n                  </ul>\n                </motion.div>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </Section>\n\n      {/* Placeholder sections for now */}\n      {Array.from({ length: 3 }, (_, i) => (\n        <Section key={i + 7} id={`section-${i + 7}`} background=\"default\">\n          <div className=\"text-center\">\n            <h2 className=\"text-4xl font-bold mb-4 text-brand-text\">\n              Section {i + 7} - Coming Soon\n            </h2>\n            <p className=\"text-brand-text/70\">\n              This section will be built in the next steps\n            </p>\n          </div>\n        </Section>\n      ))}\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAWe,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,gBAAgB;IAEtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM;+CAAe;oBACnB,MAAM,WAAW,SAAS,gBAAgB,CAAC;oBAC3C,MAAM,iBAAiB,OAAO,OAAO,GAAG,OAAO,WAAW,GAAG;oBAE7D,SAAS,OAAO;uDAAC,CAAC,SAAS;4BACzB,MAAM,UAAU;4BAChB,MAAM,MAAM,QAAQ,SAAS;4BAC7B,MAAM,SAAS,MAAM,QAAQ,YAAY;4BAEzC,IAAI,kBAAkB,OAAO,iBAAiB,QAAQ;gCACpD,kBAAkB,QAAQ;4BAC5B;wBACF;;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;kCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;yBAAG,EAAE;IAEL,qBACE,6LAAC;QAAK,WAAU;;0BACd,6LAAC,mIAAA,CAAA,UAAU;gBAAC,gBAAgB;gBAAgB,eAAe;;;;;;0BAG3D,6LAAC,gIAAA,CAAA,UAAO;gBAAC,IAAG;gBAAY,YAAW;;kCACjC,6LAAC,4IAAA,CAAA,UAAmB;;;;;kCACpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAG,MAAM;gCAAU;gCAC3C,WAAU;;kDAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wCACR,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;kDACzC;;;;;;kDAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wCACP,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAG,OAAO;wCAAI;kDACvC;;;;;;;;;;;;0CAKH,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,SAAS,IAAM,SAAS,cAAc,CAAC,cAAc,eAAe;wCAAE,UAAU;oCAAS;gCACzF,WAAU;;kDAEV,6LAAC;wCAAK,WAAU;;4CAAgB;0DAE9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;gDACV,SAAS;oDAAE,GAAG;wDAAC;wDAAG;wDAAG;qDAAE;gDAAC;gDACxB,YAAY;oDAAE,UAAU;oDAAK,QAAQ;gDAAS;gDAC9C,WAAU;0DACX;;;;;;;;;;;;kDAIH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAI;wCACzB,YAAY;4CAAE,UAAU;wCAAI;;;;;;;;;;;;;;;;;;;;;;;;0BAOpC,6LAAC,gIAAA,CAAA,UAAO;gBAAC,IAAG;gBAAY,YAAW;0BACjC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;sCACX;;;;;;sCAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,aAAa;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BACpC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCAEV,cAAA,6LAAC;gCAAK,WAAU;0CAA8C;;;;;;;;;;;sCAKhE,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCACE,OAAO;oCACP,aAAa;oCACb,OAAO;gCACT;gCACA;oCACE,OAAO;oCACP,aAAa;oCACb,OAAO;gCACT;gCACA;oCACE,OAAO;oCACP,aAAa;oCACb,OAAO;gCACT;6BACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;wCAAI,SAAS,CAAC;oCAAG;oCAC3C,aAAa;wCAAE,SAAS;wCAAG,GAAG;wCAAG,SAAS;oCAAE;oCAC5C,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,YAAY;wCACV,GAAG,CAAC;wCACJ,OAAO;wCACP,YAAY;4CAAE,UAAU;wCAAI;oCAC9B;oCACA,WAAW,CAAC,2BAA2B,EAAE,QAAQ,KAAK,CAAC,8HAA8H,CAAC;;sDAEtL,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;;;;;;;;;;8DAEjB,6LAAC;oDAAG,WAAU;8DACX,QAAQ,KAAK;;;;;;8DAEhB,6LAAC;oDAAE,WAAU;8DACV,QAAQ,WAAW;;;;;;;;;;;;;mCApBnB;;;;;;;;;;;;;;;;;;;;;0BA8Bf,6LAAC,gIAAA,CAAA,UAAO;gBAAC,IAAG;gBAAY,YAAW;0BACjC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;8CAAsD;;;;;;8CAGpE,6LAAC;oCAAE,WAAU;8CAA+C;;;;;;;;;;;;sCAK9D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mIAAA,CAAA,UAAU;oCACT,MAAK;oCACL,OAAM;oCACN,aAAY;oCACZ,OAAO;;;;;;8CAET,6LAAC,mIAAA,CAAA,UAAU;oCACT,MAAK;oCACL,OAAM;oCACN,aAAY;oCACZ,OAAO;;;;;;8CAET,6LAAC,mIAAA,CAAA,UAAU;oCACT,MAAK;oCACL,OAAM;oCACN,aAAY;oCACZ,OAAO;;;;;;8CAET,6LAAC,mIAAA,CAAA,UAAU;oCACT,MAAK;oCACL,OAAM;oCACN,aAAY;oCACZ,OAAO;;;;;;;;;;;;;;;;;;;;;;;0BAOf,6LAAC,gIAAA,CAAA,UAAO;gBAAC,IAAG;gBAAY,YAAW;0BACjC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,WAAU;8CACX;;;;;;8CAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,iIAAA,CAAA,UAAQ;oCACP,QAAO;oCACP,OAAM;oCACN,aAAY;oCACZ,OAAO;oCACP,MAAK;;;;;;8CAEP,6LAAC,iIAAA,CAAA,UAAQ;oCACP,QAAO;oCACP,OAAM;oCACN,aAAY;oCACZ,OAAO;oCACP,MAAK;;;;;;8CAEP,6LAAC,iIAAA,CAAA,UAAQ;oCACP,QAAO;oCACP,OAAM;oCACN,aAAY;oCACZ,OAAO;oCACP,MAAK;;;;;;8CAEP,6LAAC,iIAAA,CAAA,UAAQ;oCACP,QAAO;oCACP,OAAM;oCACN,aAAY;oCACZ,OAAO;oCACP,MAAK;;;;;;;;;;;;sCAKT,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;;sDAE5B,6LAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAG1D,6LAAC;4CAAI,WAAU;sDACZ;gDACC;gDACA;gDACA;gDACA;6CACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,SAAS;wDAAE,SAAS;wDAAG,GAAG,CAAC;oDAAG;oDAC9B,aAAa;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAChC,YAAY;wDAAE,UAAU;wDAAK,OAAO,QAAQ;oDAAI;oDAChD,WAAU;;sEAEV,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAK,WAAU;sEAAsB;;;;;;;mDAPjC;;;;;;;;;;;;;;;;8CAab,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,6LAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAG3D,6LAAC;oDAAE,WAAU;8DAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAW9C,6LAAC,gIAAA,CAAA,UAAO;gBAAC,IAAG;gBAAY,YAAW;0BACjC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,WAAU;8CACX;;;;;;8CAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;8CACX;;;;;;;;;;;;sCAKH,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;;sDAE5B,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAI3D,6LAAC;4CAAI,WAAU;sDACZ;gDACC;oDACE,OAAO;oDACP,aAAa;gDACf;gDACA;oDACE,OAAO;oDACP,aAAa;gDACf;gDACA;oDACE,OAAO;oDACP,aAAa;gDACf;6CACD,CAAC,GAAG,CAAC,CAAC,UAAU,sBACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,aAAa;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAChC,YAAY;wDAAE,UAAU;wDAAK,OAAO,QAAQ;oDAAI;oDAChD,WAAU;;sEAEV,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;;;;;;;;;;;sEAEjB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EACX,SAAS,KAAK;;;;;;8EAEjB,6LAAC;oEAAE,WAAU;8EACV,SAAS,WAAW;;;;;;;;;;;;;mDAdpB;;;;;;;;;;;;;;;;8CAuBb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,WAAU;8CAGV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;sEAAgC;;;;;;;;;;;kEAElD,6LAAC;wDAAG,WAAU;kEAA+C;;;;;;kEAC7D,6LAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;0DAG5C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAW,WAAU;kEAAkE;;;;;;kEAIxF,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;;;;;;;;;;;kFAEjB,6LAAC;kFAAK;;;;;;;;;;;;0EAER,6LAAC;gEAAK,WAAU;0EAA8B;;;;;;0EAC9C,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;;;;;;;;;;;kFAEjB,6LAAC;kFAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWtB,6LAAC,gIAAA,CAAA,UAAO;gBAAC,IAAG;gBAAY,YAAW;0BACjC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;8CAAsD;;;;;;8CAGpE,6LAAC;oCAAE,WAAU;8CAAoD;;;;;;;;;;;;sCAMnE,6LAAC,kIAAA,CAAA,UAAS;;;;;sCAGV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAW,WAAU;kDAA6D;;;;;;kDAInF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,WAAU;;kEAEV,6LAAC;wDAAG,WAAU;kEAA+C;;;;;;kEAG7D,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;;;;;;;;;;;;;0DAIR,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,WAAU;;kEAEV,6LAAC;wDAAG,WAAU;kEAA+C;;;;;;kEAG7D,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUjB,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,CAAC,GAAG,kBAC7B,6LAAC,gIAAA,CAAA,UAAO;oBAAa,IAAI,CAAC,QAAQ,EAAE,IAAI,GAAG;oBAAE,YAAW;8BACtD,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAA0C;oCAC7C,IAAI;oCAAE;;;;;;;0CAEjB,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;mBALxB,IAAI;;;;;;;;;;;AAa1B;GA7fwB;KAAA", "debugId": null}}]}