'use client';

import { motion } from 'framer-motion';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  animated?: boolean;
  className?: string;
}

const sizeClasses = {
  sm: 'w-8 h-8',
  md: 'w-12 h-12',
  lg: 'w-16 h-16',
  xl: 'w-24 h-24'
};

export default function Logo({ 
  size = 'md', 
  animated = true, 
  className = '' 
}: LogoProps) {
  const logoVariants = {
    initial: { 
      scale: 0,
      rotate: -180,
      opacity: 0 
    },
    animate: { 
      scale: 1,
      rotate: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 200,
        damping: 15,
        duration: 1
      }
    },
    hover: {
      scale: 1.1,
      rotate: 5,
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 10
      }
    }
  };

  const pathVariants = {
    initial: { pathLength: 0, opacity: 0 },
    animate: { 
      pathLength: 1, 
      opacity: 1,
      transition: {
        pathLength: { duration: 2, ease: "easeInOut" },
        opacity: { duration: 0.5 }
      }
    }
  };

  return (
    <motion.div
      className={`${sizeClasses[size]} ${className}`}
      variants={animated ? logoVariants : undefined}
      initial={animated ? "initial" : undefined}
      animate={animated ? "animate" : undefined}
      whileHover={animated ? "hover" : undefined}
    >
      <svg
        viewBox="0 0 100 100"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="w-full h-full"
      >
        {/* Background Circle */}
        <motion.circle
          cx="50"
          cy="50"
          r="45"
          fill="url(#logoGradient)"
          variants={animated ? pathVariants : undefined}
          initial={animated ? "initial" : undefined}
          animate={animated ? "animate" : undefined}
        />
        
        {/* Property Plaza Icon - Stylized Building */}
        <motion.path
          d="M25 70 L25 40 L35 30 L50 20 L65 30 L75 40 L75 70 Z"
          fill="white"
          fillOpacity="0.9"
          variants={animated ? pathVariants : undefined}
          initial={animated ? "initial" : undefined}
          animate={animated ? "animate" : undefined}
        />
        
        {/* Building Details */}
        <motion.rect
          x="30"
          y="45"
          width="8"
          height="8"
          fill="var(--primary)"
          variants={animated ? pathVariants : undefined}
          initial={animated ? "initial" : undefined}
          animate={animated ? "animate" : undefined}
        />
        
        <motion.rect
          x="42"
          y="45"
          width="8"
          height="8"
          fill="var(--primary)"
          variants={animated ? pathVariants : undefined}
          initial={animated ? "initial" : undefined}
          animate={animated ? "animate" : undefined}
        />
        
        <motion.rect
          x="54"
          y="45"
          width="8"
          height="8"
          fill="var(--primary)"
          variants={animated ? pathVariants : undefined}
          initial={animated ? "initial" : undefined}
          animate={animated ? "animate" : undefined}
        />
        
        <motion.rect
          x="30"
          y="57"
          width="8"
          height="8"
          fill="var(--primary)"
          variants={animated ? pathVariants : undefined}
          initial={animated ? "initial" : undefined}
          animate={animated ? "animate" : undefined}
        />
        
        <motion.rect
          x="54"
          y="57"
          width="8"
          height="8"
          fill="var(--primary)"
          variants={animated ? pathVariants : undefined}
          initial={animated ? "initial" : undefined}
          animate={animated ? "animate" : undefined}
        />
        
        {/* Door */}
        <motion.rect
          x="42"
          y="57"
          width="8"
          height="13"
          fill="var(--accent)"
          variants={animated ? pathVariants : undefined}
          initial={animated ? "initial" : undefined}
          animate={animated ? "animate" : undefined}
        />
        
        {/* Gradient Definition */}
        <defs>
          <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="var(--primary)" />
            <stop offset="100%" stopColor="var(--accent)" />
          </linearGradient>
        </defs>
      </svg>
    </motion.div>
  );
}

interface LogoTextProps {
  size?: 'sm' | 'md' | 'lg';
  animated?: boolean;
  className?: string;
}

const textSizeClasses = {
  sm: 'text-lg',
  md: 'text-2xl',
  lg: 'text-4xl'
};

export function LogoText({ 
  size = 'md', 
  animated = true, 
  className = '' 
}: LogoTextProps) {
  return (
    <motion.div
      className={`font-bold ${textSizeClasses[size]} ${className}`}
      initial={animated ? { opacity: 0, x: -20 } : undefined}
      animate={animated ? { opacity: 1, x: 0 } : undefined}
      transition={animated ? { duration: 0.8, delay: 0.5 } : undefined}
    >
      <span className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
        Property Plaza
      </span>
    </motion.div>
  );
}

export function LogoWithText({ 
  size = 'md', 
  animated = true, 
  className = '' 
}: LogoProps) {
  return (
    <motion.div
      className={`flex items-center space-x-3 ${className}`}
      initial={animated ? { opacity: 0, y: -20 } : undefined}
      animate={animated ? { opacity: 1, y: 0 } : undefined}
      transition={animated ? { duration: 0.8 } : undefined}
    >
      <Logo size={size} animated={animated} />
      <LogoText size={size} animated={animated} />
    </motion.div>
  );
}
