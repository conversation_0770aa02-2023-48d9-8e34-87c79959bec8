'use client';

import { motion, useScroll, useSpring } from 'framer-motion';
import { ReactNode, useEffect, useState } from 'react';

interface ScrollContainerProps {
  children: ReactNode;
  className?: string;
}

export default function ScrollContainer({
  children,
  className = ''
}: ScrollContainerProps) {
  const { scrollYProgress } = useScroll();
  const scaleX = useSpring(scrollYProgress, {
    stiffness: 100,
    damping: 30,
    restDelta: 0.001
  });

  return (
    <>
      {/* Scroll Progress Bar */}
      <motion.div
        className="fixed top-0 left-0 right-0 h-1 bg-primary z-50 origin-left"
        style={{ scaleX }}
      />

      <motion.div
        className={`scroll-smooth ${className}`}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        {children}
      </motion.div>
    </>
  );
}

interface SectionProps {
  children: ReactNode;
  className?: string;
  id?: string;
  fullHeight?: boolean;
}

export function Section({
  children,
  className = '',
  id,
  fullHeight = true
}: SectionProps) {
  const [isInView, setIsInView] = useState(false);

  return (
    <motion.section
      id={id}
      className={`
        section-snap flex flex-col justify-center items-center
        px-4 md:px-8 lg:px-16 py-8 md:py-16
        ${fullHeight ? 'min-h-screen' : 'min-h-[80vh]'}
        ${className}
      `.trim().replace(/\s+/g, ' ')}
      initial={{ opacity: 0, y: 50 }}
      whileInView={{
        opacity: 1,
        y: 0,
        transition: { duration: 0.8, ease: "easeOut" }
      }}
      onViewportEnter={() => setIsInView(true)}
      viewport={{ once: true, amount: 0.2 }}
    >
      <div className="max-w-7xl w-full h-full flex flex-col justify-center">
        <motion.div
          initial={{ opacity: 0 }}
          animate={isInView ? { opacity: 1 } : { opacity: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {children}
        </motion.div>
      </div>
    </motion.section>
  );
}
