'use client';

import { motion } from 'framer-motion';
import { ReactNode } from 'react';

interface ScrollContainerProps {
  children: ReactNode;
  className?: string;
}

export default function ScrollContainer({ 
  children, 
  className = '' 
}: ScrollContainerProps) {
  return (
    <motion.div 
      className={`scroll-smooth ${className}`}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {children}
    </motion.div>
  );
}

interface SectionProps {
  children: ReactNode;
  className?: string;
  id?: string;
}

export function Section({ 
  children, 
  className = '', 
  id 
}: SectionProps) {
  return (
    <motion.section
      id={id}
      className={`section-snap flex flex-col justify-center items-center px-4 md:px-8 lg:px-16 ${className}`}
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
      viewport={{ once: true, amount: 0.3 }}
    >
      <div className="max-w-7xl w-full">
        {children}
      </div>
    </motion.section>
  );
}
