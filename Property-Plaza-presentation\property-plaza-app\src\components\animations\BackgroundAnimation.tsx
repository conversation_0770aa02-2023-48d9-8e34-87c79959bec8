'use client';

import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';

interface ParticleProps {
  x: number;
  y: number;
  size: number;
  delay: number;
}

function Particle({ x, y, size, delay }: ParticleProps) {
  return (
    <motion.div
      className="absolute rounded-full bg-primary/20"
      style={{
        left: `${x}%`,
        top: `${y}%`,
        width: size,
        height: size,
      }}
      initial={{ opacity: 0, scale: 0 }}
      animate={{ 
        opacity: [0, 0.6, 0],
        scale: [0, 1, 0],
        y: [0, -50, -100]
      }}
      transition={{
        duration: 4,
        delay,
        repeat: Infinity,
        ease: "easeOut"
      }}
    />
  );
}

export function ParticleBackground() {
  const [particles, setParticles] = useState<ParticleProps[]>([]);

  useEffect(() => {
    const generateParticles = () => {
      const newParticles: ParticleProps[] = [];
      for (let i = 0; i < 20; i++) {
        newParticles.push({
          x: Math.random() * 100,
          y: Math.random() * 100,
          size: Math.random() * 8 + 4,
          delay: Math.random() * 4
        });
      }
      setParticles(newParticles);
    };

    generateParticles();
  }, []);

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {particles.map((particle, index) => (
        <Particle key={index} {...particle} />
      ))}
    </div>
  );
}

export function GradientOrb({ 
  className = '',
  size = 400,
  color = 'primary'
}: {
  className?: string;
  size?: number;
  color?: 'primary' | 'accent';
}) {
  const colorClass = color === 'primary' ? 'bg-primary/10' : 'bg-accent/10';
  
  return (
    <motion.div
      className={`absolute rounded-full blur-3xl ${colorClass} ${className}`}
      style={{ width: size, height: size }}
      animate={{
        scale: [1, 1.2, 1],
        opacity: [0.3, 0.6, 0.3],
      }}
      transition={{
        duration: 8,
        repeat: Infinity,
        ease: "easeInOut"
      }}
    />
  );
}

export function FloatingElements() {
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {/* Large gradient orbs */}
      <GradientOrb
        className="-top-48 -left-48"
        size={600}
        color="primary"
      />
      <GradientOrb
        className="-bottom-48 -right-48"
        size={500}
        color="accent"
      />

      {/* Additional gradient orbs for more depth */}
      <GradientOrb
        className="top-1/4 -right-32"
        size={300}
        color="primary"
      />
      <GradientOrb
        className="-bottom-32 left-1/4"
        size={400}
        color="accent"
      />

      {/* Floating geometric shapes */}
      <motion.div
        className="absolute top-1/4 left-1/4 w-4 h-4 bg-primary/30 rounded-full"
        animate={{
          y: [0, -20, 0],
          x: [0, 10, 0],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />

      <motion.div
        className="absolute top-3/4 right-1/4 w-6 h-6 bg-accent/30 rotate-45"
        animate={{
          y: [0, -30, 0],
          x: [0, -15, 0],
          rotate: [45, 135, 45],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />

      <motion.div
        className="absolute top-1/2 right-1/3 w-3 h-3 bg-primary/40 rounded-full"
        animate={{
          y: [0, -25, 0],
          x: [0, 20, 0],
        }}
        transition={{
          duration: 7,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1
        }}
      />

      {/* Additional floating elements */}
      <motion.div
        className="absolute top-1/3 left-1/2 w-2 h-2 bg-accent/50 rounded-full"
        animate={{
          y: [0, -15, 0],
          x: [0, -10, 0],
          scale: [1, 1.5, 1],
        }}
        transition={{
          duration: 5,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 2
        }}
      />

      <motion.div
        className="absolute bottom-1/3 left-1/5 w-5 h-5 bg-primary/25 rounded-full"
        animate={{
          y: [0, -35, 0],
          x: [0, 25, 0],
          opacity: [0.25, 0.6, 0.25],
        }}
        transition={{
          duration: 9,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 0.5
        }}
      />

      {/* Subtle grid pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="w-full h-full" style={{
          backgroundImage: `
            linear-gradient(var(--primary) 1px, transparent 1px),
            linear-gradient(90deg, var(--primary) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px'
        }} />
      </div>
    </div>
  );
}
