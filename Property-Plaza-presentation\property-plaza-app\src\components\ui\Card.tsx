'use client';

import { motion } from 'framer-motion';
import { ReactNode } from 'react';

interface CardProps {
  children: ReactNode;
  className?: string;
  hover?: boolean;
  delay?: number;
}

interface PillarCardProps {
  icon: ReactNode;
  title: string;
  description: string;
  delay?: number;
  className?: string;
}

export default function Card({ 
  children, 
  className = '', 
  hover = true,
  delay = 0 
}: CardProps) {
  return (
    <motion.div
      className={`
        bg-white rounded-2xl p-6 shadow-lg
        border border-primary/10
        ${hover ? 'hover:shadow-xl hover:border-primary/20' : ''}
        transition-all duration-300
        ${className}
      `.trim().replace(/\s+/g, ' ')}
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ 
        duration: 0.6, 
        delay,
        ease: "easeOut" 
      }}
      viewport={{ once: true, amount: 0.3 }}
      whileHover={hover ? { 
        y: -5,
        transition: { type: "spring", stiffness: 300, damping: 20 }
      } : {}}
    >
      {children}
    </motion.div>
  );
}

export function PillarCard({ 
  icon, 
  title, 
  description, 
  delay = 0,
  className = '' 
}: PillarCardProps) {
  return (
    <Card delay={delay} className={className}>
      <div className="text-center space-y-4">
        <motion.div 
          className="w-16 h-16 bg-primary/10 rounded-full mx-auto flex items-center justify-center text-2xl"
          whileHover={{ 
            scale: 1.1,
            backgroundColor: "var(--primary)",
            color: "white"
          }}
          transition={{ duration: 0.3 }}
        >
          {icon}
        </motion.div>
        <h3 className="text-xl font-bold text-text-default">
          {title}
        </h3>
        <p className="text-text-muted leading-relaxed">
          {description}
        </p>
      </div>
    </Card>
  );
}

interface StatCardProps {
  number: string;
  label: string;
  description?: string;
  delay?: number;
}

export function StatCard({ 
  number, 
  label, 
  description, 
  delay = 0 
}: StatCardProps) {
  return (
    <Card delay={delay} className="text-center">
      <motion.div
        className="text-4xl md:text-5xl font-bold text-primary mb-2"
        initial={{ scale: 0 }}
        whileInView={{ scale: 1 }}
        transition={{ 
          type: "spring", 
          stiffness: 200, 
          damping: 15,
          delay: delay + 0.2 
        }}
        viewport={{ once: true }}
      >
        {number}
      </motion.div>
      <h4 className="text-lg font-semibold text-text-default mb-1">
        {label}
      </h4>
      {description && (
        <p className="text-sm text-text-muted">
          {description}
        </p>
      )}
    </Card>
  );
}
