{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/Property-Plaza-presentation/property-plaza-app/src/components/ui/ScrollContainer.tsx"], "sourcesContent": ["'use client';\n\nimport { motion, useScroll, useSpring } from 'framer-motion';\nimport { ReactNode, useEffect, useState } from 'react';\n\ninterface ScrollContainerProps {\n  children: ReactNode;\n  className?: string;\n}\n\nexport default function ScrollContainer({\n  children,\n  className = ''\n}: ScrollContainerProps) {\n  const { scrollYProgress } = useScroll();\n  const scaleX = useSpring(scrollYProgress, {\n    stiffness: 100,\n    damping: 30,\n    restDelta: 0.001\n  });\n\n  return (\n    <>\n      {/* Scroll Progress Bar */}\n      <motion.div\n        className=\"fixed top-0 left-0 right-0 h-1 bg-primary z-50 origin-left\"\n        style={{ scaleX }}\n      />\n\n      <motion.div\n        className={`scroll-smooth ${className}`}\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ duration: 0.5 }}\n      >\n        {children}\n      </motion.div>\n    </>\n  );\n}\n\ninterface SectionProps {\n  children: ReactNode;\n  className?: string;\n  id?: string;\n  fullHeight?: boolean;\n}\n\nexport function Section({\n  children,\n  className = '',\n  id,\n  fullHeight = true\n}: SectionProps) {\n  const [isInView, setIsInView] = useState(false);\n\n  return (\n    <motion.section\n      id={id}\n      className={`\n        section-snap flex flex-col justify-center items-center\n        px-4 md:px-8 lg:px-16 py-8 md:py-16\n        ${fullHeight ? 'min-h-screen' : 'min-h-[80vh]'}\n        ${className}\n      `.trim().replace(/\\s+/g, ' ')}\n      initial={{ opacity: 0, y: 50 }}\n      whileInView={{\n        opacity: 1,\n        y: 0,\n        transition: { duration: 0.8, ease: \"easeOut\" }\n      }}\n      onViewportEnter={() => setIsInView(true)}\n      viewport={{ once: true, amount: 0.2 }}\n    >\n      <div className=\"max-w-7xl w-full h-full flex flex-col justify-center\">\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={isInView ? { opacity: 1 } : { opacity: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n        >\n          {children}\n        </motion.div>\n      </div>\n    </motion.section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AAAA;AACA;AAHA;;;;AAUe,SAAS,gBAAgB,EACtC,QAAQ,EACR,YAAY,EAAE,EACO;IACrB,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD;IACpC,MAAM,SAAS,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB;QACxC,WAAW;QACX,SAAS;QACT,WAAW;IACb;IAEA,qBACE;;0BAEE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBAAE;gBAAO;;;;;;0BAGlB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,CAAC,cAAc,EAAE,WAAW;gBACvC,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,UAAU;gBAAI;0BAE3B;;;;;;;;AAIT;AASO,SAAS,QAAQ,EACtB,QAAQ,EACR,YAAY,EAAE,EACd,EAAE,EACF,aAAa,IAAI,EACJ;IACb,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;QACb,IAAI;QACJ,WAAW,CAAC;;;QAGV,EAAE,aAAa,iBAAiB,eAAe;QAC/C,EAAE,UAAU;MACd,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC,QAAQ;QACzB,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YACX,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAU;QAC/C;QACA,iBAAiB,IAAM,YAAY;QACnC,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAI;kBAEpC,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS,WAAW;oBAAE,SAAS;gBAAE,IAAI;oBAAE,SAAS;gBAAE;gBAClD,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;0BAEvC;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 132, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/Property-Plaza-presentation/property-plaza-app/src/components/ui/SlideIndicator.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\n\ninterface SlideIndicatorProps {\n  currentSlide: number;\n  totalSlides: number;\n  className?: string;\n}\n\nconst sectionTitles = [\n  'Hero',\n  'Problem',\n  'Mission',\n  'About',\n  'Partnership',\n  'Synergy',\n  'Pilot',\n  'Metrics',\n  'CTA'\n];\n\nexport default function SlideIndicator({\n  currentSlide,\n  totalSlides,\n  className = ''\n}: SlideIndicatorProps) {\n  const progress = (currentSlide / totalSlides) * 100;\n\n  return (\n    <motion.div\n      className={`fixed bottom-8 right-8 z-50 bg-white/10 backdrop-blur-md rounded-2xl p-4 ${className}`}\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ delay: 1 }}\n    >\n      <div className=\"flex flex-col items-end space-y-3\">\n        {/* Current section info */}\n        <div className=\"text-right\">\n          <div className=\"text-sm font-medium text-text-default\">\n            {sectionTitles[currentSlide - 1] || 'Section'}\n          </div>\n          <div className=\"text-xs text-text-muted\">\n            {currentSlide} of {totalSlides}\n          </div>\n        </div>\n\n        {/* Progress bar */}\n        <div className=\"w-24 h-1 bg-text-muted/20 rounded-full overflow-hidden\">\n          <motion.div\n            className=\"h-full bg-primary rounded-full\"\n            initial={{ width: 0 }}\n            animate={{ width: `${progress}%` }}\n            transition={{ duration: 0.5, ease: \"easeOut\" }}\n          />\n        </div>\n\n        {/* Dot indicators */}\n        <div className=\"flex space-x-1\">\n          {Array.from({ length: totalSlides }, (_, i) => (\n            <motion.div\n              key={i}\n              className={`w-2 h-2 rounded-full transition-all duration-300 ${\n                i + 1 === currentSlide\n                  ? 'bg-primary scale-125'\n                  : 'bg-text-muted/30'\n              }`}\n              whileHover={{ scale: 1.3 }}\n              initial={{ scale: 0 }}\n              animate={{ scale: i + 1 === currentSlide ? 1.25 : 1 }}\n              transition={{\n                type: \"spring\",\n                stiffness: 300,\n                damping: 20,\n                delay: i * 0.1\n              }}\n            />\n          ))}\n        </div>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAUA,MAAM,gBAAgB;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS,eAAe,EACrC,YAAY,EACZ,WAAW,EACX,YAAY,EAAE,EACM;IACpB,MAAM,WAAW,AAAC,eAAe,cAAe;IAEhD,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,yEAAyE,EAAE,WAAW;QAClG,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,OAAO;QAAE;kBAEvB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACZ,aAAa,CAAC,eAAe,EAAE,IAAI;;;;;;sCAEtC,8OAAC;4BAAI,WAAU;;gCACZ;gCAAa;gCAAK;;;;;;;;;;;;;8BAKvB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,OAAO;wBAAE;wBACpB,SAAS;4BAAE,OAAO,GAAG,SAAS,CAAC,CAAC;wBAAC;wBACjC,YAAY;4BAAE,UAAU;4BAAK,MAAM;wBAAU;;;;;;;;;;;8BAKjD,8OAAC;oBAAI,WAAU;8BACZ,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAY,GAAG,CAAC,GAAG,kBACvC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAW,CAAC,iDAAiD,EAC3D,IAAI,MAAM,eACN,yBACA,oBACJ;4BACF,YAAY;gCAAE,OAAO;4BAAI;4BACzB,SAAS;gCAAE,OAAO;4BAAE;4BACpB,SAAS;gCAAE,OAAO,IAAI,MAAM,eAAe,OAAO;4BAAE;4BACpD,YAAY;gCACV,MAAM;gCACN,WAAW;gCACX,SAAS;gCACT,OAAO,IAAI;4BACb;2BAdK;;;;;;;;;;;;;;;;;;;;;AAqBnB", "debugId": null}}, {"offset": {"line": 271, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/Property-Plaza-presentation/property-plaza-app/src/components/ui/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useState, useEffect } from 'react';\n\ninterface NavigationProps {\n  currentSlide: number;\n  totalSlides: number;\n  onSlideChange: (slide: number) => void;\n}\n\nconst sections = [\n  { id: 'hero', label: 'Hero', title: 'Empower Property Decisions' },\n  { id: 'problem', label: 'Problem', title: 'Market Challenges' },\n  { id: 'mission', label: 'Mission', title: '4 Pillars' },\n  { id: 'about', label: 'About', title: 'Property Plaza' },\n  { id: 'partnership', label: 'Partnership', title: 'Paradise Indonesia' },\n  { id: 'synergy', label: 'Synergy', title: 'Joint Value' },\n  { id: 'pilot', label: 'Pilot', title: 'Campaign Plan' },\n  { id: 'metrics', label: 'Metrics', title: 'KPIs & Tracking' },\n  { id: 'cta', label: 'CTA', title: 'Let\\'s Launch' }\n];\n\nexport default function Navigation({ \n  currentSlide, \n  totalSlides, \n  onSlideChange \n}: NavigationProps) {\n  const [isOpen, setIsOpen] = useState(false);\n\n  const scrollToSection = (sectionId: string, slideIndex: number) => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      element.scrollIntoView({ \n        behavior: 'smooth',\n        block: 'start'\n      });\n      onSlideChange(slideIndex);\n      setIsOpen(false);\n    }\n  };\n\n  const goToNextSlide = () => {\n    if (currentSlide < totalSlides) {\n      const nextSection = sections[currentSlide];\n      scrollToSection(nextSection.id, currentSlide + 1);\n    }\n  };\n\n  const goToPrevSlide = () => {\n    if (currentSlide > 1) {\n      const prevSection = sections[currentSlide - 2];\n      scrollToSection(prevSection.id, currentSlide - 1);\n    }\n  };\n\n  // Keyboard navigation\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if (e.key === 'ArrowDown' || e.key === ' ') {\n        e.preventDefault();\n        goToNextSlide();\n      } else if (e.key === 'ArrowUp') {\n        e.preventDefault();\n        goToPrevSlide();\n      } else if (e.key === 'Escape') {\n        setIsOpen(false);\n      }\n    };\n\n    window.addEventListener('keydown', handleKeyDown);\n    return () => window.removeEventListener('keydown', handleKeyDown);\n  }, [currentSlide]);\n\n  return (\n    <>\n      {/* Navigation Toggle Button */}\n      <motion.button\n        className=\"fixed top-8 left-8 z-50 bg-white/10 backdrop-blur-md rounded-full p-3 hover:bg-white/20 transition-colors\"\n        onClick={() => setIsOpen(!isOpen)}\n        whileHover={{ scale: 1.05 }}\n        whileTap={{ scale: 0.95 }}\n        initial={{ opacity: 0, x: -20 }}\n        animate={{ opacity: 1, x: 0 }}\n        transition={{ delay: 1 }}\n      >\n        <motion.div\n          className=\"w-6 h-6 flex flex-col justify-center items-center\"\n          animate={isOpen ? \"open\" : \"closed\"}\n        >\n          <motion.span\n            className=\"w-5 h-0.5 bg-text-default block\"\n            variants={{\n              closed: { rotate: 0, y: 0 },\n              open: { rotate: 45, y: 2 }\n            }}\n          />\n          <motion.span\n            className=\"w-5 h-0.5 bg-text-default block mt-1\"\n            variants={{\n              closed: { opacity: 1 },\n              open: { opacity: 0 }\n            }}\n          />\n          <motion.span\n            className=\"w-5 h-0.5 bg-text-default block mt-1\"\n            variants={{\n              closed: { rotate: 0, y: 0 },\n              open: { rotate: -45, y: -2 }\n            }}\n          />\n        </motion.div>\n      </motion.button>\n\n      {/* Navigation Menu */}\n      <motion.nav\n        className=\"fixed top-20 left-8 z-40 bg-white/95 backdrop-blur-md rounded-2xl shadow-xl overflow-hidden\"\n        initial={{ opacity: 0, x: -20, scale: 0.9 }}\n        animate={{ \n          opacity: isOpen ? 1 : 0,\n          x: isOpen ? 0 : -20,\n          scale: isOpen ? 1 : 0.9,\n          pointerEvents: isOpen ? 'auto' : 'none'\n        }}\n        transition={{ duration: 0.3, ease: \"easeOut\" }}\n      >\n        <div className=\"p-4 space-y-2 min-w-[280px]\">\n          <h3 className=\"text-sm font-semibold text-text-muted uppercase tracking-wide mb-4\">\n            Presentation Sections\n          </h3>\n          {sections.map((section, index) => (\n            <motion.button\n              key={section.id}\n              className={`\n                w-full text-left p-3 rounded-lg transition-all duration-200\n                ${currentSlide === index + 1 \n                  ? 'bg-primary text-white shadow-md' \n                  : 'hover:bg-primary/10 text-text-default'\n                }\n              `}\n              onClick={() => scrollToSection(section.id, index + 1)}\n              whileHover={{ x: currentSlide === index + 1 ? 0 : 4 }}\n              whileTap={{ scale: 0.98 }}\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <div className=\"font-medium\">{section.title}</div>\n                  <div className={`text-sm ${\n                    currentSlide === index + 1 ? 'text-white/80' : 'text-text-muted'\n                  }`}>\n                    Section {index + 1}\n                  </div>\n                </div>\n                <div className={`\n                  w-2 h-2 rounded-full transition-colors\n                  ${currentSlide === index + 1 ? 'bg-white' : 'bg-primary/30'}\n                `} />\n              </div>\n            </motion.button>\n          ))}\n        </div>\n      </motion.nav>\n\n      {/* Navigation Arrows */}\n      <div className=\"fixed right-8 top-1/2 -translate-y-1/2 z-50 space-y-4\">\n        <motion.button\n          className={`\n            p-3 rounded-full bg-white/10 backdrop-blur-md transition-all duration-300\n            ${currentSlide > 1 \n              ? 'hover:bg-white/20 text-text-default' \n              : 'opacity-50 cursor-not-allowed text-text-muted'\n            }\n          `}\n          onClick={goToPrevSlide}\n          disabled={currentSlide <= 1}\n          whileHover={currentSlide > 1 ? { scale: 1.05 } : {}}\n          whileTap={currentSlide > 1 ? { scale: 0.95 } : {}}\n          initial={{ opacity: 0, x: 20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ delay: 1.2 }}\n        >\n          <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 15l7-7 7 7\" />\n          </svg>\n        </motion.button>\n        \n        <motion.button\n          className={`\n            p-3 rounded-full bg-white/10 backdrop-blur-md transition-all duration-300\n            ${currentSlide < totalSlides \n              ? 'hover:bg-white/20 text-text-default' \n              : 'opacity-50 cursor-not-allowed text-text-muted'\n            }\n          `}\n          onClick={goToNextSlide}\n          disabled={currentSlide >= totalSlides}\n          whileHover={currentSlide < totalSlides ? { scale: 1.05 } : {}}\n          whileTap={currentSlide < totalSlides ? { scale: 0.95 } : {}}\n          initial={{ opacity: 0, x: 20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ delay: 1.4 }}\n        >\n          <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n          </svg>\n        </motion.button>\n      </div>\n\n      {/* Keyboard Hints */}\n      <motion.div\n        className=\"fixed bottom-8 left-8 z-50 bg-white/10 backdrop-blur-md rounded-lg px-4 py-2\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 2 }}\n      >\n        <div className=\"text-sm text-text-muted\">\n          <span className=\"font-medium\">Navigation:</span> ↑↓ arrows, Space, or click\n        </div>\n      </motion.div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAWA,MAAM,WAAW;IACf;QAAE,IAAI;QAAQ,OAAO;QAAQ,OAAO;IAA6B;IACjE;QAAE,IAAI;QAAW,OAAO;QAAW,OAAO;IAAoB;IAC9D;QAAE,IAAI;QAAW,OAAO;QAAW,OAAO;IAAY;IACtD;QAAE,IAAI;QAAS,OAAO;QAAS,OAAO;IAAiB;IACvD;QAAE,IAAI;QAAe,OAAO;QAAe,OAAO;IAAqB;IACvE;QAAE,IAAI;QAAW,OAAO;QAAW,OAAO;IAAc;IACxD;QAAE,IAAI;QAAS,OAAO;QAAS,OAAO;IAAgB;IACtD;QAAE,IAAI;QAAW,OAAO;QAAW,OAAO;IAAkB;IAC5D;QAAE,IAAI;QAAO,OAAO;QAAO,OAAO;IAAgB;CACnD;AAEc,SAAS,WAAW,EACjC,YAAY,EACZ,WAAW,EACX,aAAa,EACG;IAChB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,kBAAkB,CAAC,WAAmB;QAC1C,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBACrB,UAAU;gBACV,OAAO;YACT;YACA,cAAc;YACd,UAAU;QACZ;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,eAAe,aAAa;YAC9B,MAAM,cAAc,QAAQ,CAAC,aAAa;YAC1C,gBAAgB,YAAY,EAAE,EAAE,eAAe;QACjD;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,eAAe,GAAG;YACpB,MAAM,cAAc,QAAQ,CAAC,eAAe,EAAE;YAC9C,gBAAgB,YAAY,EAAE,EAAE,eAAe;QACjD;IACF;IAEA,sBAAsB;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,IAAI,EAAE,GAAG,KAAK,eAAe,EAAE,GAAG,KAAK,KAAK;gBAC1C,EAAE,cAAc;gBAChB;YACF,OAAO,IAAI,EAAE,GAAG,KAAK,WAAW;gBAC9B,EAAE,cAAc;gBAChB;YACF,OAAO,IAAI,EAAE,GAAG,KAAK,UAAU;gBAC7B,UAAU;YACZ;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG;QAAC;KAAa;IAEjB,qBACE;;0BAEE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,WAAU;gBACV,SAAS,IAAM,UAAU,CAAC;gBAC1B,YAAY;oBAAE,OAAO;gBAAK;gBAC1B,UAAU;oBAAE,OAAO;gBAAK;gBACxB,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAE;0BAEvB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS,SAAS,SAAS;;sCAE3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4BACV,WAAU;4BACV,UAAU;gCACR,QAAQ;oCAAE,QAAQ;oCAAG,GAAG;gCAAE;gCAC1B,MAAM;oCAAE,QAAQ;oCAAI,GAAG;gCAAE;4BAC3B;;;;;;sCAEF,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4BACV,WAAU;4BACV,UAAU;gCACR,QAAQ;oCAAE,SAAS;gCAAE;gCACrB,MAAM;oCAAE,SAAS;gCAAE;4BACrB;;;;;;sCAEF,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4BACV,WAAU;4BACV,UAAU;gCACR,QAAQ;oCAAE,QAAQ;oCAAG,GAAG;gCAAE;gCAC1B,MAAM;oCAAE,QAAQ,CAAC;oCAAI,GAAG,CAAC;gCAAE;4BAC7B;;;;;;;;;;;;;;;;;0BAMN,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;oBAAI,OAAO;gBAAI;gBAC1C,SAAS;oBACP,SAAS,SAAS,IAAI;oBACtB,GAAG,SAAS,IAAI,CAAC;oBACjB,OAAO,SAAS,IAAI;oBACpB,eAAe,SAAS,SAAS;gBACnC;gBACA,YAAY;oBAAE,UAAU;oBAAK,MAAM;gBAAU;0BAE7C,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqE;;;;;;wBAGlF,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCAEZ,WAAW,CAAC;;gBAEV,EAAE,iBAAiB,QAAQ,IACvB,oCACA,wCACH;cACH,CAAC;gCACD,SAAS,IAAM,gBAAgB,QAAQ,EAAE,EAAE,QAAQ;gCACnD,YAAY;oCAAE,GAAG,iBAAiB,QAAQ,IAAI,IAAI;gCAAE;gCACpD,UAAU;oCAAE,OAAO;gCAAK;0CAExB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAAe,QAAQ,KAAK;;;;;;8DAC3C,8OAAC;oDAAI,WAAW,CAAC,QAAQ,EACvB,iBAAiB,QAAQ,IAAI,kBAAkB,mBAC/C;;wDAAE;wDACO,QAAQ;;;;;;;;;;;;;sDAGrB,8OAAC;4CAAI,WAAW,CAAC;;kBAEf,EAAE,iBAAiB,QAAQ,IAAI,aAAa,gBAAgB;gBAC9D,CAAC;;;;;;;;;;;;+BAxBE,QAAQ,EAAE;;;;;;;;;;;;;;;;0BAgCvB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,WAAW,CAAC;;YAEV,EAAE,eAAe,IACb,wCACA,gDACH;UACH,CAAC;wBACD,SAAS;wBACT,UAAU,gBAAgB;wBAC1B,YAAY,eAAe,IAAI;4BAAE,OAAO;wBAAK,IAAI,CAAC;wBAClD,UAAU,eAAe,IAAI;4BAAE,OAAO;wBAAK,IAAI,CAAC;wBAChD,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;kCAEzB,cAAA,8OAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;kCAIzE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,WAAW,CAAC;;YAEV,EAAE,eAAe,cACb,wCACA,gDACH;UACH,CAAC;wBACD,SAAS;wBACT,UAAU,gBAAgB;wBAC1B,YAAY,eAAe,cAAc;4BAAE,OAAO;wBAAK,IAAI,CAAC;wBAC5D,UAAU,eAAe,cAAc;4BAAE,OAAO;wBAAK,IAAI,CAAC;wBAC1D,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;kCAEzB,cAAA,8OAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;0BAM3E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAE;0BAEvB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;sCAAc;;;;;;wBAAkB;;;;;;;;;;;;;;AAK1D", "debugId": null}}, {"offset": {"line": 711, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/Property-Plaza-presentation/property-plaza-app/src/components/ui/Button.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { ReactNode } from 'react';\n\ninterface ButtonProps {\n  children: ReactNode;\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';\n  size?: 'sm' | 'md' | 'lg' | 'xl';\n  className?: string;\n  onClick?: () => void;\n  disabled?: boolean;\n  href?: string;\n  target?: string;\n  rel?: string;\n}\n\nconst buttonVariants = {\n  primary: 'bg-primary hover:bg-primary-dark text-white shadow-md hover:shadow-lg',\n  secondary: 'bg-accent hover:bg-accent-dark text-white shadow-md hover:shadow-lg',\n  outline: 'border-2 border-primary text-primary hover:bg-primary hover:text-white',\n  ghost: 'text-primary hover:bg-primary/10'\n};\n\nconst buttonSizes = {\n  sm: 'px-4 py-2 text-sm',\n  md: 'px-6 py-3 text-base',\n  lg: 'px-8 py-4 text-lg',\n  xl: 'px-10 py-5 text-xl'\n};\n\nexport default function Button({\n  children,\n  variant = 'primary',\n  size = 'md',\n  className = '',\n  onClick,\n  disabled = false,\n  href,\n  target,\n  rel\n}: ButtonProps) {\n  const baseClasses = `\n    inline-flex items-center justify-center\n    font-semibold rounded-full\n    transition-all duration-300 ease-out\n    focus:outline-none focus:ring-4 focus:ring-primary/20\n    disabled:opacity-50 disabled:cursor-not-allowed\n    ${buttonVariants[variant]}\n    ${buttonSizes[size]}\n    ${className}\n  `.trim().replace(/\\s+/g, ' ');\n\n  const MotionComponent = motion.button;\n\n  if (href) {\n    return (\n      <motion.a\n        href={href}\n        target={target}\n        rel={rel}\n        className={baseClasses}\n        whileHover={{ scale: disabled ? 1 : 1.05 }}\n        whileTap={{ scale: disabled ? 1 : 0.95 }}\n        transition={{ type: \"spring\", stiffness: 400, damping: 17 }}\n      >\n        {children}\n      </motion.a>\n    );\n  }\n\n  return (\n    <MotionComponent\n      className={baseClasses}\n      onClick={onClick}\n      disabled={disabled}\n      whileHover={{ scale: disabled ? 1 : 1.05 }}\n      whileTap={{ scale: disabled ? 1 : 0.95 }}\n      transition={{ type: \"spring\", stiffness: 400, damping: 17 }}\n    >\n      {children}\n    </MotionComponent>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAiBA,MAAM,iBAAiB;IACrB,SAAS;IACT,WAAW;IACX,SAAS;IACT,OAAO;AACT;AAEA,MAAM,cAAc;IAClB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEe,SAAS,OAAO,EAC7B,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,OAAO,EACP,WAAW,KAAK,EAChB,IAAI,EACJ,MAAM,EACN,GAAG,EACS;IACZ,MAAM,cAAc,CAAC;;;;;;IAMnB,EAAE,cAAc,CAAC,QAAQ,CAAC;IAC1B,EAAE,WAAW,CAAC,KAAK,CAAC;IACpB,EAAE,UAAU;EACd,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC,QAAQ;IAEzB,MAAM,kBAAkB,0LAAA,CAAA,SAAM,CAAC,MAAM;IAErC,IAAI,MAAM;QACR,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;YACP,MAAM;YACN,QAAQ;YACR,KAAK;YACL,WAAW;YACX,YAAY;gBAAE,OAAO,WAAW,IAAI;YAAK;YACzC,UAAU;gBAAE,OAAO,WAAW,IAAI;YAAK;YACvC,YAAY;gBAAE,MAAM;gBAAU,WAAW;gBAAK,SAAS;YAAG;sBAEzD;;;;;;IAGP;IAEA,qBACE,8OAAC;QACC,WAAW;QACX,SAAS;QACT,UAAU;QACV,YAAY;YAAE,OAAO,WAAW,IAAI;QAAK;QACzC,UAAU;YAAE,OAAO,WAAW,IAAI;QAAK;QACvC,YAAY;YAAE,MAAM;YAAU,WAAW;YAAK,SAAS;QAAG;kBAEzD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 795, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/Property-Plaza-presentation/property-plaza-app/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { ReactNode } from 'react';\n\ninterface CardProps {\n  children: ReactNode;\n  className?: string;\n  hover?: boolean;\n  delay?: number;\n}\n\ninterface PillarCardProps {\n  icon: ReactNode;\n  title: string;\n  description: string;\n  delay?: number;\n  className?: string;\n}\n\nexport default function Card({ \n  children, \n  className = '', \n  hover = true,\n  delay = 0 \n}: CardProps) {\n  return (\n    <motion.div\n      className={`\n        bg-white rounded-2xl p-6 shadow-lg\n        border border-primary/10\n        ${hover ? 'hover:shadow-xl hover:border-primary/20' : ''}\n        transition-all duration-300\n        ${className}\n      `.trim().replace(/\\s+/g, ' ')}\n      initial={{ opacity: 0, y: 30 }}\n      whileInView={{ opacity: 1, y: 0 }}\n      transition={{ \n        duration: 0.6, \n        delay,\n        ease: \"easeOut\" \n      }}\n      viewport={{ once: true, amount: 0.3 }}\n      whileHover={hover ? { \n        y: -5,\n        transition: { type: \"spring\", stiffness: 300, damping: 20 }\n      } : {}}\n    >\n      {children}\n    </motion.div>\n  );\n}\n\nexport function PillarCard({ \n  icon, \n  title, \n  description, \n  delay = 0,\n  className = '' \n}: PillarCardProps) {\n  return (\n    <Card delay={delay} className={className}>\n      <div className=\"text-center space-y-4\">\n        <motion.div \n          className=\"w-16 h-16 bg-primary/10 rounded-full mx-auto flex items-center justify-center text-2xl\"\n          whileHover={{ \n            scale: 1.1,\n            backgroundColor: \"var(--primary)\",\n            color: \"white\"\n          }}\n          transition={{ duration: 0.3 }}\n        >\n          {icon}\n        </motion.div>\n        <h3 className=\"text-xl font-bold text-text-default\">\n          {title}\n        </h3>\n        <p className=\"text-text-muted leading-relaxed\">\n          {description}\n        </p>\n      </div>\n    </Card>\n  );\n}\n\ninterface StatCardProps {\n  number: string;\n  label: string;\n  description?: string;\n  delay?: number;\n}\n\nexport function StatCard({ \n  number, \n  label, \n  description, \n  delay = 0 \n}: StatCardProps) {\n  return (\n    <Card delay={delay} className=\"text-center\">\n      <motion.div\n        className=\"text-4xl md:text-5xl font-bold text-primary mb-2\"\n        initial={{ scale: 0 }}\n        whileInView={{ scale: 1 }}\n        transition={{ \n          type: \"spring\", \n          stiffness: 200, \n          damping: 15,\n          delay: delay + 0.2 \n        }}\n        viewport={{ once: true }}\n      >\n        {number}\n      </motion.div>\n      <h4 className=\"text-lg font-semibold text-text-default mb-1\">\n        {label}\n      </h4>\n      {description && (\n        <p className=\"text-sm text-text-muted\">\n          {description}\n        </p>\n      )}\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAoBe,SAAS,KAAK,EAC3B,QAAQ,EACR,YAAY,EAAE,EACd,QAAQ,IAAI,EACZ,QAAQ,CAAC,EACC;IACV,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC;;;QAGV,EAAE,QAAQ,4CAA4C,GAAG;;QAEzD,EAAE,UAAU;MACd,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC,QAAQ;QACzB,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YACV,UAAU;YACV;YACA,MAAM;QACR;QACA,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAI;QACpC,YAAY,QAAQ;YAClB,GAAG,CAAC;YACJ,YAAY;gBAAE,MAAM;gBAAU,WAAW;gBAAK,SAAS;YAAG;QAC5D,IAAI,CAAC;kBAEJ;;;;;;AAGP;AAEO,SAAS,WAAW,EACzB,IAAI,EACJ,KAAK,EACL,WAAW,EACX,QAAQ,CAAC,EACT,YAAY,EAAE,EACE;IAChB,qBACE,8OAAC;QAAK,OAAO;QAAO,WAAW;kBAC7B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,YAAY;wBACV,OAAO;wBACP,iBAAiB;wBACjB,OAAO;oBACT;oBACA,YAAY;wBAAE,UAAU;oBAAI;8BAE3B;;;;;;8BAEH,8OAAC;oBAAG,WAAU;8BACX;;;;;;8BAEH,8OAAC;oBAAE,WAAU;8BACV;;;;;;;;;;;;;;;;;AAKX;AASO,SAAS,SAAS,EACvB,MAAM,EACN,KAAK,EACL,WAAW,EACX,QAAQ,CAAC,EACK;IACd,qBACE,8OAAC;QAAK,OAAO;QAAO,WAAU;;0BAC5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,OAAO;gBAAE;gBACpB,aAAa;oBAAE,OAAO;gBAAE;gBACxB,YAAY;oBACV,MAAM;oBACN,WAAW;oBACX,SAAS;oBACT,OAAO,QAAQ;gBACjB;gBACA,UAAU;oBAAE,MAAM;gBAAK;0BAEtB;;;;;;0BAEH,8OAAC;gBAAG,WAAU;0BACX;;;;;;YAEF,6BACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 954, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/Property-Plaza-presentation/property-plaza-app/src/components/ui/Typography.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { ReactNode } from 'react';\n\ninterface TypographyProps {\n  children: ReactNode;\n  className?: string;\n  animate?: boolean;\n  delay?: number;\n}\n\ninterface HeadingProps extends TypographyProps {\n  level?: 1 | 2 | 3 | 4 | 5 | 6;\n  gradient?: boolean;\n}\n\nexport function Heading({ \n  children, \n  level = 1, \n  className = '', \n  animate = true,\n  delay = 0,\n  gradient = false \n}: HeadingProps) {\n  const Tag = `h${level}` as keyof JSX.IntrinsicElements;\n  \n  const baseClasses = {\n    1: 'text-5xl md:text-7xl font-bold leading-tight',\n    2: 'text-4xl md:text-6xl font-bold leading-tight',\n    3: 'text-3xl md:text-4xl font-bold leading-snug',\n    4: 'text-2xl md:text-3xl font-semibold leading-snug',\n    5: 'text-xl md:text-2xl font-semibold leading-normal',\n    6: 'text-lg md:text-xl font-semibold leading-normal'\n  };\n\n  const gradientClass = gradient \n    ? 'bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent' \n    : 'text-text-default';\n\n  const classes = `${baseClasses[level]} ${gradientClass} ${className}`;\n\n  if (!animate) {\n    return <Tag className={classes}>{children}</Tag>;\n  }\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 30 }}\n      whileInView={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.8, delay, ease: \"easeOut\" }}\n      viewport={{ once: true, amount: 0.3 }}\n    >\n      <Tag className={classes}>{children}</Tag>\n    </motion.div>\n  );\n}\n\nexport function Paragraph({ \n  children, \n  className = '', \n  animate = true,\n  delay = 0 \n}: TypographyProps) {\n  const classes = `text-lg md:text-xl text-text-muted leading-relaxed ${className}`;\n\n  if (!animate) {\n    return <p className={classes}>{children}</p>;\n  }\n\n  return (\n    <motion.p\n      className={classes}\n      initial={{ opacity: 0, y: 20 }}\n      whileInView={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.6, delay, ease: \"easeOut\" }}\n      viewport={{ once: true, amount: 0.3 }}\n    >\n      {children}\n    </motion.p>\n  );\n}\n\nexport function Quote({ \n  children, \n  author, \n  className = '', \n  animate = true,\n  delay = 0 \n}: TypographyProps & { author?: string }) {\n  if (!animate) {\n    return (\n      <blockquote className={`text-2xl md:text-3xl font-medium text-text-default italic text-center ${className}`}>\n        \"{children}\"\n        {author && (\n          <footer className=\"text-lg text-text-muted mt-4 not-italic\">\n            — {author}\n          </footer>\n        )}\n      </blockquote>\n    );\n  }\n\n  return (\n    <motion.blockquote\n      className={`text-2xl md:text-3xl font-medium text-text-default italic text-center ${className}`}\n      initial={{ opacity: 0, scale: 0.9 }}\n      whileInView={{ opacity: 1, scale: 1 }}\n      transition={{ duration: 0.8, delay, ease: \"easeOut\" }}\n      viewport={{ once: true, amount: 0.3 }}\n    >\n      <motion.span\n        initial={{ opacity: 0 }}\n        whileInView={{ opacity: 1 }}\n        transition={{ duration: 0.6, delay: delay + 0.2 }}\n        viewport={{ once: true }}\n      >\n        \"{children}\"\n      </motion.span>\n      {author && (\n        <motion.footer\n          className=\"text-lg text-text-muted mt-4 not-italic\"\n          initial={{ opacity: 0, y: 10 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: delay + 0.4 }}\n          viewport={{ once: true }}\n        >\n          — {author}\n        </motion.footer>\n      )}\n    </motion.blockquote>\n  );\n}\n\nexport function Badge({ \n  children, \n  variant = 'primary',\n  className = '' \n}: { \n  children: ReactNode; \n  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';\n  className?: string;\n}) {\n  const variants = {\n    primary: 'bg-primary/10 text-primary border-primary/20',\n    secondary: 'bg-accent/10 text-accent border-accent/20',\n    success: 'bg-success/10 text-success border-success/20',\n    warning: 'bg-warning/10 text-warning border-warning/20',\n    error: 'bg-error/10 text-error border-error/20'\n  };\n\n  return (\n    <span className={`\n      inline-flex items-center px-3 py-1 rounded-full text-sm font-medium\n      border ${variants[variant]} ${className}\n    `.trim().replace(/\\s+/g, ' ')}>\n      {children}\n    </span>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AAFA;;;AAiBO,SAAS,QAAQ,EACtB,QAAQ,EACR,QAAQ,CAAC,EACT,YAAY,EAAE,EACd,UAAU,IAAI,EACd,QAAQ,CAAC,EACT,WAAW,KAAK,EACH;IACb,MAAM,MAAM,CAAC,CAAC,EAAE,OAAO;IAEvB,MAAM,cAAc;QAClB,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;IACL;IAEA,MAAM,gBAAgB,WAClB,0EACA;IAEJ,MAAM,UAAU,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,EAAE,cAAc,CAAC,EAAE,WAAW;IAErE,IAAI,CAAC,SAAS;QACZ,qBAAO,8OAAC;YAAI,WAAW;sBAAU;;;;;;IACnC;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK;YAAO,MAAM;QAAU;QACpD,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAI;kBAEpC,cAAA,8OAAC;YAAI,WAAW;sBAAU;;;;;;;;;;;AAGhC;AAEO,SAAS,UAAU,EACxB,QAAQ,EACR,YAAY,EAAE,EACd,UAAU,IAAI,EACd,QAAQ,CAAC,EACO;IAChB,MAAM,UAAU,CAAC,mDAAmD,EAAE,WAAW;IAEjF,IAAI,CAAC,SAAS;QACZ,qBAAO,8OAAC;YAAE,WAAW;sBAAU;;;;;;IACjC;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;QACP,WAAW;QACX,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK;YAAO,MAAM;QAAU;QACpD,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAI;kBAEnC;;;;;;AAGP;AAEO,SAAS,MAAM,EACpB,QAAQ,EACR,MAAM,EACN,YAAY,EAAE,EACd,UAAU,IAAI,EACd,QAAQ,CAAC,EAC6B;IACtC,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAW,WAAW,CAAC,sEAAsE,EAAE,WAAW;;gBAAE;gBACzG;gBAAS;gBACV,wBACC,8OAAC;oBAAO,WAAU;;wBAA0C;wBACvD;;;;;;;;;;;;;IAKb;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,UAAU;QAChB,WAAW,CAAC,sEAAsE,EAAE,WAAW;QAC/F,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,aAAa;YAAE,SAAS;YAAG,OAAO;QAAE;QACpC,YAAY;YAAE,UAAU;YAAK;YAAO,MAAM;QAAU;QACpD,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAI;;0BAEpC,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,aAAa;oBAAE,SAAS;gBAAE;gBAC1B,YAAY;oBAAE,UAAU;oBAAK,OAAO,QAAQ;gBAAI;gBAChD,UAAU;oBAAE,MAAM;gBAAK;;oBACxB;oBACG;oBAAS;;;;;;;YAEZ,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,aAAa;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAChC,YAAY;oBAAE,UAAU;oBAAK,OAAO,QAAQ;gBAAI;gBAChD,UAAU;oBAAE,MAAM;gBAAK;;oBACxB;oBACI;;;;;;;;;;;;;AAKb;AAEO,SAAS,MAAM,EACpB,QAAQ,EACR,UAAU,SAAS,EACnB,YAAY,EAAE,EAKf;IACC,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,SAAS;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QAAK,WAAW,CAAC;;aAET,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,UAAU;IAC1C,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC,QAAQ;kBACtB;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 1186, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/Property-Plaza-presentation/property-plaza-app/src/components/animations/BackgroundAnimation.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useEffect, useState } from 'react';\n\ninterface ParticleProps {\n  x: number;\n  y: number;\n  size: number;\n  delay: number;\n}\n\nfunction Particle({ x, y, size, delay }: ParticleProps) {\n  return (\n    <motion.div\n      className=\"absolute rounded-full bg-primary/20\"\n      style={{\n        left: `${x}%`,\n        top: `${y}%`,\n        width: size,\n        height: size,\n      }}\n      initial={{ opacity: 0, scale: 0 }}\n      animate={{ \n        opacity: [0, 0.6, 0],\n        scale: [0, 1, 0],\n        y: [0, -50, -100]\n      }}\n      transition={{\n        duration: 4,\n        delay,\n        repeat: Infinity,\n        ease: \"easeOut\"\n      }}\n    />\n  );\n}\n\nexport function ParticleBackground() {\n  const [particles, setParticles] = useState<ParticleProps[]>([]);\n\n  useEffect(() => {\n    const generateParticles = () => {\n      const newParticles: ParticleProps[] = [];\n      for (let i = 0; i < 20; i++) {\n        newParticles.push({\n          x: Math.random() * 100,\n          y: Math.random() * 100,\n          size: Math.random() * 8 + 4,\n          delay: Math.random() * 4\n        });\n      }\n      setParticles(newParticles);\n    };\n\n    generateParticles();\n  }, []);\n\n  return (\n    <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n      {particles.map((particle, index) => (\n        <Particle key={index} {...particle} />\n      ))}\n    </div>\n  );\n}\n\nexport function GradientOrb({ \n  className = '',\n  size = 400,\n  color = 'primary'\n}: {\n  className?: string;\n  size?: number;\n  color?: 'primary' | 'accent';\n}) {\n  const colorClass = color === 'primary' ? 'bg-primary/10' : 'bg-accent/10';\n  \n  return (\n    <motion.div\n      className={`absolute rounded-full blur-3xl ${colorClass} ${className}`}\n      style={{ width: size, height: size }}\n      animate={{\n        scale: [1, 1.2, 1],\n        opacity: [0.3, 0.6, 0.3],\n      }}\n      transition={{\n        duration: 8,\n        repeat: Infinity,\n        ease: \"easeInOut\"\n      }}\n    />\n  );\n}\n\nexport function FloatingElements() {\n  return (\n    <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n      {/* Large gradient orbs */}\n      <GradientOrb\n        className=\"-top-48 -left-48\"\n        size={600}\n        color=\"primary\"\n      />\n      <GradientOrb\n        className=\"-bottom-48 -right-48\"\n        size={500}\n        color=\"accent\"\n      />\n\n      {/* Additional gradient orbs for more depth */}\n      <GradientOrb\n        className=\"top-1/4 -right-32\"\n        size={300}\n        color=\"primary\"\n      />\n      <GradientOrb\n        className=\"-bottom-32 left-1/4\"\n        size={400}\n        color=\"accent\"\n      />\n\n      {/* Floating geometric shapes */}\n      <motion.div\n        className=\"absolute top-1/4 left-1/4 w-4 h-4 bg-primary/30 rounded-full\"\n        animate={{\n          y: [0, -20, 0],\n          x: [0, 10, 0],\n        }}\n        transition={{\n          duration: 6,\n          repeat: Infinity,\n          ease: \"easeInOut\"\n        }}\n      />\n\n      <motion.div\n        className=\"absolute top-3/4 right-1/4 w-6 h-6 bg-accent/30 rotate-45\"\n        animate={{\n          y: [0, -30, 0],\n          x: [0, -15, 0],\n          rotate: [45, 135, 45],\n        }}\n        transition={{\n          duration: 8,\n          repeat: Infinity,\n          ease: \"easeInOut\"\n        }}\n      />\n\n      <motion.div\n        className=\"absolute top-1/2 right-1/3 w-3 h-3 bg-primary/40 rounded-full\"\n        animate={{\n          y: [0, -25, 0],\n          x: [0, 20, 0],\n        }}\n        transition={{\n          duration: 7,\n          repeat: Infinity,\n          ease: \"easeInOut\",\n          delay: 1\n        }}\n      />\n\n      {/* Additional floating elements */}\n      <motion.div\n        className=\"absolute top-1/3 left-1/2 w-2 h-2 bg-accent/50 rounded-full\"\n        animate={{\n          y: [0, -15, 0],\n          x: [0, -10, 0],\n          scale: [1, 1.5, 1],\n        }}\n        transition={{\n          duration: 5,\n          repeat: Infinity,\n          ease: \"easeInOut\",\n          delay: 2\n        }}\n      />\n\n      <motion.div\n        className=\"absolute bottom-1/3 left-1/5 w-5 h-5 bg-primary/25 rounded-full\"\n        animate={{\n          y: [0, -35, 0],\n          x: [0, 25, 0],\n          opacity: [0.25, 0.6, 0.25],\n        }}\n        transition={{\n          duration: 9,\n          repeat: Infinity,\n          ease: \"easeInOut\",\n          delay: 0.5\n        }}\n      />\n\n      {/* Subtle grid pattern */}\n      <div className=\"absolute inset-0 opacity-5\">\n        <div className=\"w-full h-full\" style={{\n          backgroundImage: `\n            linear-gradient(var(--primary) 1px, transparent 1px),\n            linear-gradient(90deg, var(--primary) 1px, transparent 1px)\n          `,\n          backgroundSize: '50px 50px'\n        }} />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAHA;;;;AAYA,SAAS,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAiB;IACpD,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,OAAO;YACL,MAAM,GAAG,EAAE,CAAC,CAAC;YACb,KAAK,GAAG,EAAE,CAAC,CAAC;YACZ,OAAO;YACP,QAAQ;QACV;QACA,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,SAAS;YACP,SAAS;gBAAC;gBAAG;gBAAK;aAAE;YACpB,OAAO;gBAAC;gBAAG;gBAAG;aAAE;YAChB,GAAG;gBAAC;gBAAG,CAAC;gBAAI,CAAC;aAAI;QACnB;QACA,YAAY;YACV,UAAU;YACV;YACA,QAAQ;YACR,MAAM;QACR;;;;;;AAGN;AAEO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IAE9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB;YACxB,MAAM,eAAgC,EAAE;YACxC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;gBAC3B,aAAa,IAAI,CAAC;oBAChB,GAAG,KAAK,MAAM,KAAK;oBACnB,GAAG,KAAK,MAAM,KAAK;oBACnB,MAAM,KAAK,MAAM,KAAK,IAAI;oBAC1B,OAAO,KAAK,MAAM,KAAK;gBACzB;YACF;YACA,aAAa;QACf;QAEA;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;kBACZ,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,8OAAC;gBAAsB,GAAG,QAAQ;eAAnB;;;;;;;;;;AAIvB;AAEO,SAAS,YAAY,EAC1B,YAAY,EAAE,EACd,OAAO,GAAG,EACV,QAAQ,SAAS,EAKlB;IACC,MAAM,aAAa,UAAU,YAAY,kBAAkB;IAE3D,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,+BAA+B,EAAE,WAAW,CAAC,EAAE,WAAW;QACtE,OAAO;YAAE,OAAO;YAAM,QAAQ;QAAK;QACnC,SAAS;YACP,OAAO;gBAAC;gBAAG;gBAAK;aAAE;YAClB,SAAS;gBAAC;gBAAK;gBAAK;aAAI;QAC1B;QACA,YAAY;YACV,UAAU;YACV,QAAQ;YACR,MAAM;QACR;;;;;;AAGN;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,WAAU;gBACV,MAAM;gBACN,OAAM;;;;;;0BAER,8OAAC;gBACC,WAAU;gBACV,MAAM;gBACN,OAAM;;;;;;0BAIR,8OAAC;gBACC,WAAU;gBACV,MAAM;gBACN,OAAM;;;;;;0BAER,8OAAC;gBACC,WAAU;gBACV,MAAM;gBACN,OAAM;;;;;;0BAIR,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC;wBAAG,CAAC;wBAAI;qBAAE;oBACd,GAAG;wBAAC;wBAAG;wBAAI;qBAAE;gBACf;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;;;;;;0BAGF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC;wBAAG,CAAC;wBAAI;qBAAE;oBACd,GAAG;wBAAC;wBAAG,CAAC;wBAAI;qBAAE;oBACd,QAAQ;wBAAC;wBAAI;wBAAK;qBAAG;gBACvB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;;;;;;0BAGF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC;wBAAG,CAAC;wBAAI;qBAAE;oBACd,GAAG;wBAAC;wBAAG;wBAAI;qBAAE;gBACf;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;oBACN,OAAO;gBACT;;;;;;0BAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC;wBAAG,CAAC;wBAAI;qBAAE;oBACd,GAAG;wBAAC;wBAAG,CAAC;wBAAI;qBAAE;oBACd,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;gBACpB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;oBACN,OAAO;gBACT;;;;;;0BAGF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC;wBAAG,CAAC;wBAAI;qBAAE;oBACd,GAAG;wBAAC;wBAAG;wBAAI;qBAAE;oBACb,SAAS;wBAAC;wBAAM;wBAAK;qBAAK;gBAC5B;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;oBACN,OAAO;gBACT;;;;;;0BAIF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;oBAAgB,OAAO;wBACpC,iBAAiB,CAAC;;;UAGlB,CAAC;wBACD,gBAAgB;oBAClB;;;;;;;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 1515, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/Property-Plaza-presentation/property-plaza-app/src/components/sections/HeroStats.tsx"], "sourcesContent": ["'use client';\n\nimport { motion, useInView } from 'framer-motion';\nimport { useRef, useState, useEffect } from 'react';\n\ninterface StatItemProps {\n  number: string;\n  label: string;\n  suffix?: string;\n  delay?: number;\n}\n\nfunction StatItem({ number, label, suffix = '', delay = 0 }: StatItemProps) {\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true });\n  const [count, setCount] = useState(0);\n  \n  const targetNumber = parseInt(number.replace(/[^\\d]/g, ''));\n\n  useEffect(() => {\n    if (isInView) {\n      const timer = setTimeout(() => {\n        let start = 0;\n        const increment = targetNumber / 30;\n        const counter = setInterval(() => {\n          start += increment;\n          if (start >= targetNumber) {\n            setCount(targetNumber);\n            clearInterval(counter);\n          } else {\n            setCount(Math.floor(start));\n          }\n        }, 50);\n        \n        return () => clearInterval(counter);\n      }, delay * 1000);\n      \n      return () => clearTimeout(timer);\n    }\n  }, [isInView, targetNumber, delay]);\n\n  return (\n    <motion.div\n      ref={ref}\n      className=\"text-center\"\n      initial={{ opacity: 0, y: 20 }}\n      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}\n      transition={{ duration: 0.6, delay }}\n    >\n      <motion.div\n        className=\"text-3xl md:text-4xl font-bold text-primary mb-2\"\n        initial={{ scale: 0 }}\n        animate={isInView ? { scale: 1 } : { scale: 0 }}\n        transition={{ \n          type: \"spring\", \n          stiffness: 200, \n          damping: 15,\n          delay: delay + 0.2 \n        }}\n      >\n        {count}{suffix}\n      </motion.div>\n      <div className=\"text-sm md:text-base text-text-muted font-medium\">\n        {label}\n      </div>\n    </motion.div>\n  );\n}\n\nexport default function HeroStats() {\n  return (\n    <motion.div\n      className=\"mt-16 pt-12 border-t border-white/20\"\n      initial={{ opacity: 0 }}\n      whileInView={{ opacity: 1 }}\n      transition={{ duration: 0.8, delay: 0.5 }}\n      viewport={{ once: true, amount: 0.3 }}\n    >\n      <motion.div\n        className=\"text-center mb-8\"\n        initial={{ opacity: 0, y: 20 }}\n        whileInView={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6 }}\n        viewport={{ once: true }}\n      >\n        <h3 className=\"text-lg md:text-xl font-semibold text-text-default mb-2\">\n          Property Plaza Impact\n        </h3>\n        <p className=\"text-sm md:text-base text-text-muted\">\n          Building trust in Bali's real estate market\n        </p>\n      </motion.div>\n\n      <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8\">\n        <StatItem\n          number=\"30\"\n          label=\"Days Since Launch\"\n          delay={0.1}\n        />\n        <StatItem\n          number=\"30\"\n          suffix=\"+\"\n          label=\"Active Listings\"\n          delay={0.2}\n        />\n        <StatItem\n          number=\"350\"\n          suffix=\"+\"\n          label=\"Unique Visitors\"\n          delay={0.3}\n        />\n        <StatItem\n          number=\"5\"\n          label=\"Languages Supported\"\n          delay={0.4}\n        />\n      </div>\n\n      {/* Trust Indicators */}\n      <motion.div\n        className=\"mt-12 flex flex-wrap justify-center items-center gap-6 md:gap-8\"\n        initial={{ opacity: 0, y: 20 }}\n        whileInView={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.8, delay: 0.6 }}\n        viewport={{ once: true }}\n      >\n        {[\n          { icon: \"🔒\", text: \"Legal Compliance\" },\n          { icon: \"🌐\", text: \"Multilingual Support\" },\n          { icon: \"🤖\", text: \"AI-Assisted\" },\n          { icon: \"📊\", text: \"Data-Driven\" }\n        ].map((feature, index) => (\n          <motion.div\n            key={feature.text}\n            className=\"flex items-center space-x-2 px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20\"\n            initial={{ opacity: 0, scale: 0.8 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ \n              duration: 0.5, \n              delay: 0.8 + index * 0.1,\n              type: \"spring\",\n              stiffness: 200\n            }}\n            viewport={{ once: true }}\n            whileHover={{ \n              scale: 1.05,\n              backgroundColor: \"rgba(255, 255, 255, 0.2)\"\n            }}\n          >\n            <span className=\"text-lg\">{feature.icon}</span>\n            <span className=\"text-sm font-medium text-text-default\">\n              {feature.text}\n            </span>\n          </motion.div>\n        ))}\n      </motion.div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AAHA;;;;AAYA,SAAS,SAAS,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,EAAE,QAAQ,CAAC,EAAiB;IACxE,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE,MAAM;IAAK;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe,SAAS,OAAO,OAAO,CAAC,UAAU;IAEvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ,MAAM,QAAQ,WAAW;gBACvB,IAAI,QAAQ;gBACZ,MAAM,YAAY,eAAe;gBACjC,MAAM,UAAU,YAAY;oBAC1B,SAAS;oBACT,IAAI,SAAS,cAAc;wBACzB,SAAS;wBACT,cAAc;oBAChB,OAAO;wBACL,SAAS,KAAK,KAAK,CAAC;oBACtB;gBACF,GAAG;gBAEH,OAAO,IAAM,cAAc;YAC7B,GAAG,QAAQ;YAEX,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;QAAU;QAAc;KAAM;IAElC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS,WAAW;YAAE,SAAS;YAAG,GAAG;QAAE,IAAI;YAAE,SAAS;YAAG,GAAG;QAAG;QAC/D,YAAY;YAAE,UAAU;YAAK;QAAM;;0BAEnC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,OAAO;gBAAE;gBACpB,SAAS,WAAW;oBAAE,OAAO;gBAAE,IAAI;oBAAE,OAAO;gBAAE;gBAC9C,YAAY;oBACV,MAAM;oBACN,WAAW;oBACX,SAAS;oBACT,OAAO,QAAQ;gBACjB;;oBAEC;oBAAO;;;;;;;0BAEV,8OAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAIT;AAEe,SAAS;IACtB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;QAAE;QACtB,aAAa;YAAE,SAAS;QAAE;QAC1B,YAAY;YAAE,UAAU;YAAK,OAAO;QAAI;QACxC,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAI;;0BAEpC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,aAAa;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAChC,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,UAAU;oBAAE,MAAM;gBAAK;;kCAEvB,8OAAC;wBAAG,WAAU;kCAA0D;;;;;;kCAGxE,8OAAC;wBAAE,WAAU;kCAAuC;;;;;;;;;;;;0BAKtD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,QAAO;wBACP,OAAM;wBACN,OAAO;;;;;;kCAET,8OAAC;wBACC,QAAO;wBACP,QAAO;wBACP,OAAM;wBACN,OAAO;;;;;;kCAET,8OAAC;wBACC,QAAO;wBACP,QAAO;wBACP,OAAM;wBACN,OAAO;;;;;;kCAET,8OAAC;wBACC,QAAO;wBACP,OAAM;wBACN,OAAO;;;;;;;;;;;;0BAKX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,aAAa;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAChC,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;gBACxC,UAAU;oBAAE,MAAM;gBAAK;0BAEtB;oBACC;wBAAE,MAAM;wBAAM,MAAM;oBAAmB;oBACvC;wBAAE,MAAM;wBAAM,MAAM;oBAAuB;oBAC3C;wBAAE,MAAM;wBAAM,MAAM;oBAAc;oBAClC;wBAAE,MAAM;wBAAM,MAAM;oBAAc;iBACnC,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAI;wBAClC,aAAa;4BAAE,SAAS;4BAAG,OAAO;wBAAE;wBACpC,YAAY;4BACV,UAAU;4BACV,OAAO,MAAM,QAAQ;4BACrB,MAAM;4BACN,WAAW;wBACb;wBACA,UAAU;4BAAE,MAAM;wBAAK;wBACvB,YAAY;4BACV,OAAO;4BACP,iBAAiB;wBACnB;;0CAEA,8OAAC;gCAAK,WAAU;0CAAW,QAAQ,IAAI;;;;;;0CACvC,8OAAC;gCAAK,WAAU;0CACb,QAAQ,IAAI;;;;;;;uBAlBV,QAAQ,IAAI;;;;;;;;;;;;;;;;AAyB7B", "debugId": null}}, {"offset": {"line": 1817, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/Property-Plaza-presentation/property-plaza-app/src/components/ui/Logo.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\n\ninterface LogoProps {\n  size?: 'sm' | 'md' | 'lg' | 'xl';\n  animated?: boolean;\n  className?: string;\n}\n\nconst sizeClasses = {\n  sm: 'w-8 h-8',\n  md: 'w-12 h-12',\n  lg: 'w-16 h-16',\n  xl: 'w-24 h-24'\n};\n\nexport default function Logo({ \n  size = 'md', \n  animated = true, \n  className = '' \n}: LogoProps) {\n  const logoVariants = {\n    initial: { \n      scale: 0,\n      rotate: -180,\n      opacity: 0 \n    },\n    animate: { \n      scale: 1,\n      rotate: 0,\n      opacity: 1,\n      transition: {\n        type: \"spring\",\n        stiffness: 200,\n        damping: 15,\n        duration: 1\n      }\n    },\n    hover: {\n      scale: 1.1,\n      rotate: 5,\n      transition: {\n        type: \"spring\",\n        stiffness: 400,\n        damping: 10\n      }\n    }\n  };\n\n  const pathVariants = {\n    initial: { pathLength: 0, opacity: 0 },\n    animate: { \n      pathLength: 1, \n      opacity: 1,\n      transition: {\n        pathLength: { duration: 2, ease: \"easeInOut\" },\n        opacity: { duration: 0.5 }\n      }\n    }\n  };\n\n  return (\n    <motion.div\n      className={`${sizeClasses[size]} ${className}`}\n      variants={animated ? logoVariants : undefined}\n      initial={animated ? \"initial\" : undefined}\n      animate={animated ? \"animate\" : undefined}\n      whileHover={animated ? \"hover\" : undefined}\n    >\n      <svg\n        viewBox=\"0 0 100 100\"\n        fill=\"none\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n        className=\"w-full h-full\"\n      >\n        {/* Background Circle */}\n        <motion.circle\n          cx=\"50\"\n          cy=\"50\"\n          r=\"45\"\n          fill=\"url(#logoGradient)\"\n          variants={animated ? pathVariants : undefined}\n          initial={animated ? \"initial\" : undefined}\n          animate={animated ? \"animate\" : undefined}\n        />\n        \n        {/* Property Plaza Icon - Stylized Building */}\n        <motion.path\n          d=\"M25 70 L25 40 L35 30 L50 20 L65 30 L75 40 L75 70 Z\"\n          fill=\"white\"\n          fillOpacity=\"0.9\"\n          variants={animated ? pathVariants : undefined}\n          initial={animated ? \"initial\" : undefined}\n          animate={animated ? \"animate\" : undefined}\n        />\n        \n        {/* Building Details */}\n        <motion.rect\n          x=\"30\"\n          y=\"45\"\n          width=\"8\"\n          height=\"8\"\n          fill=\"var(--primary)\"\n          variants={animated ? pathVariants : undefined}\n          initial={animated ? \"initial\" : undefined}\n          animate={animated ? \"animate\" : undefined}\n        />\n        \n        <motion.rect\n          x=\"42\"\n          y=\"45\"\n          width=\"8\"\n          height=\"8\"\n          fill=\"var(--primary)\"\n          variants={animated ? pathVariants : undefined}\n          initial={animated ? \"initial\" : undefined}\n          animate={animated ? \"animate\" : undefined}\n        />\n        \n        <motion.rect\n          x=\"54\"\n          y=\"45\"\n          width=\"8\"\n          height=\"8\"\n          fill=\"var(--primary)\"\n          variants={animated ? pathVariants : undefined}\n          initial={animated ? \"initial\" : undefined}\n          animate={animated ? \"animate\" : undefined}\n        />\n        \n        <motion.rect\n          x=\"30\"\n          y=\"57\"\n          width=\"8\"\n          height=\"8\"\n          fill=\"var(--primary)\"\n          variants={animated ? pathVariants : undefined}\n          initial={animated ? \"initial\" : undefined}\n          animate={animated ? \"animate\" : undefined}\n        />\n        \n        <motion.rect\n          x=\"54\"\n          y=\"57\"\n          width=\"8\"\n          height=\"8\"\n          fill=\"var(--primary)\"\n          variants={animated ? pathVariants : undefined}\n          initial={animated ? \"initial\" : undefined}\n          animate={animated ? \"animate\" : undefined}\n        />\n        \n        {/* Door */}\n        <motion.rect\n          x=\"42\"\n          y=\"57\"\n          width=\"8\"\n          height=\"13\"\n          fill=\"var(--accent)\"\n          variants={animated ? pathVariants : undefined}\n          initial={animated ? \"initial\" : undefined}\n          animate={animated ? \"animate\" : undefined}\n        />\n        \n        {/* Gradient Definition */}\n        <defs>\n          <linearGradient id=\"logoGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n            <stop offset=\"0%\" stopColor=\"var(--primary)\" />\n            <stop offset=\"100%\" stopColor=\"var(--accent)\" />\n          </linearGradient>\n        </defs>\n      </svg>\n    </motion.div>\n  );\n}\n\ninterface LogoTextProps {\n  size?: 'sm' | 'md' | 'lg';\n  animated?: boolean;\n  className?: string;\n}\n\nconst textSizeClasses = {\n  sm: 'text-lg',\n  md: 'text-2xl',\n  lg: 'text-4xl'\n};\n\nexport function LogoText({ \n  size = 'md', \n  animated = true, \n  className = '' \n}: LogoTextProps) {\n  return (\n    <motion.div\n      className={`font-bold ${textSizeClasses[size]} ${className}`}\n      initial={animated ? { opacity: 0, x: -20 } : undefined}\n      animate={animated ? { opacity: 1, x: 0 } : undefined}\n      transition={animated ? { duration: 0.8, delay: 0.5 } : undefined}\n    >\n      <span className=\"bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent\">\n        Property Plaza\n      </span>\n    </motion.div>\n  );\n}\n\nexport function LogoWithText({ \n  size = 'md', \n  animated = true, \n  className = '' \n}: LogoProps) {\n  return (\n    <motion.div\n      className={`flex items-center space-x-3 ${className}`}\n      initial={animated ? { opacity: 0, y: -20 } : undefined}\n      animate={animated ? { opacity: 1, y: 0 } : undefined}\n      transition={animated ? { duration: 0.8 } : undefined}\n    >\n      <Logo size={size} animated={animated} />\n      <LogoText size={size} animated={animated} />\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAUA,MAAM,cAAc;IAClB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEe,SAAS,KAAK,EAC3B,OAAO,IAAI,EACX,WAAW,IAAI,EACf,YAAY,EAAE,EACJ;IACV,MAAM,eAAe;QACnB,SAAS;YACP,OAAO;YACP,QAAQ,CAAC;YACT,SAAS;QACX;QACA,SAAS;YACP,OAAO;YACP,QAAQ;YACR,SAAS;YACT,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;gBACT,UAAU;YACZ;QACF;QACA,OAAO;YACL,OAAO;YACP,QAAQ;YACR,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;IACF;IAEA,MAAM,eAAe;QACnB,SAAS;YAAE,YAAY;YAAG,SAAS;QAAE;QACrC,SAAS;YACP,YAAY;YACZ,SAAS;YACT,YAAY;gBACV,YAAY;oBAAE,UAAU;oBAAG,MAAM;gBAAY;gBAC7C,SAAS;oBAAE,UAAU;gBAAI;YAC3B;QACF;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;QAC9C,UAAU,WAAW,eAAe;QACpC,SAAS,WAAW,YAAY;QAChC,SAAS,WAAW,YAAY;QAChC,YAAY,WAAW,UAAU;kBAEjC,cAAA,8OAAC;YACC,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAU;;8BAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oBACZ,IAAG;oBACH,IAAG;oBACH,GAAE;oBACF,MAAK;oBACL,UAAU,WAAW,eAAe;oBACpC,SAAS,WAAW,YAAY;oBAChC,SAAS,WAAW,YAAY;;;;;;8BAIlC,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oBACV,GAAE;oBACF,MAAK;oBACL,aAAY;oBACZ,UAAU,WAAW,eAAe;oBACpC,SAAS,WAAW,YAAY;oBAChC,SAAS,WAAW,YAAY;;;;;;8BAIlC,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oBACV,GAAE;oBACF,GAAE;oBACF,OAAM;oBACN,QAAO;oBACP,MAAK;oBACL,UAAU,WAAW,eAAe;oBACpC,SAAS,WAAW,YAAY;oBAChC,SAAS,WAAW,YAAY;;;;;;8BAGlC,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oBACV,GAAE;oBACF,GAAE;oBACF,OAAM;oBACN,QAAO;oBACP,MAAK;oBACL,UAAU,WAAW,eAAe;oBACpC,SAAS,WAAW,YAAY;oBAChC,SAAS,WAAW,YAAY;;;;;;8BAGlC,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oBACV,GAAE;oBACF,GAAE;oBACF,OAAM;oBACN,QAAO;oBACP,MAAK;oBACL,UAAU,WAAW,eAAe;oBACpC,SAAS,WAAW,YAAY;oBAChC,SAAS,WAAW,YAAY;;;;;;8BAGlC,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oBACV,GAAE;oBACF,GAAE;oBACF,OAAM;oBACN,QAAO;oBACP,MAAK;oBACL,UAAU,WAAW,eAAe;oBACpC,SAAS,WAAW,YAAY;oBAChC,SAAS,WAAW,YAAY;;;;;;8BAGlC,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oBACV,GAAE;oBACF,GAAE;oBACF,OAAM;oBACN,QAAO;oBACP,MAAK;oBACL,UAAU,WAAW,eAAe;oBACpC,SAAS,WAAW,YAAY;oBAChC,SAAS,WAAW,YAAY;;;;;;8BAIlC,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oBACV,GAAE;oBACF,GAAE;oBACF,OAAM;oBACN,QAAO;oBACP,MAAK;oBACL,UAAU,WAAW,eAAe;oBACpC,SAAS,WAAW,YAAY;oBAChC,SAAS,WAAW,YAAY;;;;;;8BAIlC,8OAAC;8BACC,cAAA,8OAAC;wBAAe,IAAG;wBAAe,IAAG;wBAAK,IAAG;wBAAK,IAAG;wBAAO,IAAG;;0CAC7D,8OAAC;gCAAK,QAAO;gCAAK,WAAU;;;;;;0CAC5B,8OAAC;gCAAK,QAAO;gCAAO,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1C;AAQA,MAAM,kBAAkB;IACtB,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,SAAS,SAAS,EACvB,OAAO,IAAI,EACX,WAAW,IAAI,EACf,YAAY,EAAE,EACA;IACd,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,UAAU,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;QAC5D,SAAS,WAAW;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG,IAAI;QAC7C,SAAS,WAAW;YAAE,SAAS;YAAG,GAAG;QAAE,IAAI;QAC3C,YAAY,WAAW;YAAE,UAAU;YAAK,OAAO;QAAI,IAAI;kBAEvD,cAAA,8OAAC;YAAK,WAAU;sBAAwE;;;;;;;;;;;AAK9F;AAEO,SAAS,aAAa,EAC3B,OAAO,IAAI,EACX,WAAW,IAAI,EACf,YAAY,EAAE,EACJ;IACV,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,4BAA4B,EAAE,WAAW;QACrD,SAAS,WAAW;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG,IAAI;QAC7C,SAAS,WAAW;YAAE,SAAS;YAAG,GAAG;QAAE,IAAI;QAC3C,YAAY,WAAW;YAAE,UAAU;QAAI,IAAI;;0BAE3C,8OAAC;gBAAK,MAAM;gBAAM,UAAU;;;;;;0BAC5B,8OAAC;gBAAS,MAAM;gBAAM,UAAU;;;;;;;;;;;;AAGtC", "debugId": null}}, {"offset": {"line": 2126, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/_PRIVATE/Property%20Plaza%20presentation/Property-Plaza-presentation/property-plaza-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport ScrollContainer, { Section } from '@/components/ui/ScrollContainer';\nimport SlideIndicator from '@/components/ui/SlideIndicator';\nimport Navigation from '@/components/ui/Navigation';\nimport Button from '@/components/ui/Button';\nimport { PillarCard } from '@/components/ui/Card';\nimport { Heading, Paragraph } from '@/components/ui/Typography';\nimport { FloatingElements } from '@/components/animations/BackgroundAnimation';\nimport HeroStats from '@/components/sections/HeroStats';\nimport PartnershipPreview from '@/components/sections/PartnershipPreview';\nimport { LogoWithText } from '@/components/ui/Logo';\n\nexport default function Home() {\n  const [currentSlide, setCurrentSlide] = useState(1);\n  const totalSlides = 9;\n\n  // Track scroll position to update current slide\n  useEffect(() => {\n    const handleScroll = () => {\n      const scrollPosition = window.scrollY;\n      const windowHeight = window.innerHeight;\n      const newSlide = Math.floor(scrollPosition / windowHeight) + 1;\n      setCurrentSlide(Math.min(Math.max(newSlide, 1), totalSlides));\n    };\n\n    // Throttle scroll events for better performance\n    let ticking = false;\n    const throttledHandleScroll = () => {\n      if (!ticking) {\n        requestAnimationFrame(() => {\n          handleScroll();\n          ticking = false;\n        });\n        ticking = true;\n      }\n    };\n\n    window.addEventListener('scroll', throttledHandleScroll, { passive: true });\n    return () => window.removeEventListener('scroll', throttledHandleScroll);\n  }, []);\n\n  const handleSlideChange = (slide: number) => {\n    setCurrentSlide(slide);\n  };\n\n  return (\n    <ScrollContainer>\n      {/* Navigation */}\n      <Navigation\n        currentSlide={currentSlide}\n        totalSlides={totalSlides}\n        onSlideChange={handleSlideChange}\n      />\n\n      {/* Slide Indicator */}\n      <SlideIndicator currentSlide={currentSlide} totalSlides={totalSlides} />\n\n      {/* 1. Hero Section */}\n      <Section id=\"hero\" className=\"bg-gradient-to-br from-background via-primary/5 to-accent/10 relative overflow-hidden\">\n        <FloatingElements />\n\n        {/* Hero Content */}\n        <div className=\"text-center space-y-12 relative z-10\">\n          {/* Logo */}\n          <motion.div\n            initial={{ opacity: 0, y: -30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 1, delay: 0.1 }}\n            className=\"flex justify-center\"\n          >\n            <LogoWithText size=\"lg\" />\n          </motion.div>\n\n          {/* Main Title with Staggered Animation */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            className=\"space-y-6\"\n          >\n            <motion.div\n              initial={{ opacity: 0, y: 50 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 1, delay: 0.6 }}\n            >\n              <Heading level={1} animate={false} gradient className=\"leading-tight\">\n                Empower Property Decisions\n              </Heading>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 1, delay: 0.8 }}\n            >\n              <Heading level={2} animate={false} className=\"text-accent font-medium\">\n                in Bali\n              </Heading>\n            </motion.div>\n          </motion.div>\n\n          {/* 4 Pillars Preview */}\n          <motion.div\n            initial={{ opacity: 0, y: 40 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 1, delay: 1.0 }}\n            className=\"max-w-4xl mx-auto\"\n          >\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-8\">\n              {[\n                { icon: \"🔍\", text: \"Transparency\" },\n                { icon: \"🧠\", text: \"Knowledge\" },\n                { icon: \"🤝\", text: \"Connection\" },\n                { icon: \"💪\", text: \"Empowerment\" }\n              ].map((pillar, index) => (\n                <motion.div\n                  key={pillar.text}\n                  className=\"flex flex-col items-center space-y-3 p-4 rounded-2xl bg-white/20 backdrop-blur-sm border border-white/30 hover:bg-white/30 transition-all duration-300\"\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: 1.2 + index * 0.1 }}\n                  whileHover={{\n                    y: -5,\n                    transition: { type: \"spring\", stiffness: 300, damping: 20 }\n                  }}\n                >\n                  <motion.div\n                    className=\"text-3xl md:text-4xl\"\n                    whileHover={{ scale: 1.2, rotate: 5 }}\n                    transition={{ type: \"spring\", stiffness: 400, damping: 10 }}\n                  >\n                    {pillar.icon}\n                  </motion.div>\n                  <span className=\"text-sm md:text-base font-semibold text-text-default\">\n                    {pillar.text}\n                  </span>\n                </motion.div>\n              ))}\n            </div>\n          </motion.div>\n\n          {/* CTA Section */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 1, delay: 1.6 }}\n            className=\"space-y-6\"\n          >\n            <Paragraph animate={false} className=\"text-lg md:text-xl max-w-2xl mx-auto text-text-muted\">\n              Join Property Plaza and Paradise Indonesia in revolutionizing\n              Bali's real estate market through transparency and trust.\n            </Paragraph>\n\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n              <Button\n                size=\"lg\"\n                className=\"text-lg px-8 py-4\"\n                onClick={() => {\n                  const problemSection = document.getElementById('problem');\n                  problemSection?.scrollIntoView({ behavior: 'smooth' });\n                }}\n              >\n                Start the Experience\n              </Button>\n\n              <Button\n                variant=\"outline\"\n                size=\"lg\"\n                className=\"text-lg px-8 py-4\"\n                onClick={() => {\n                  const ctaSection = document.getElementById('cta');\n                  ctaSection?.scrollIntoView({ behavior: 'smooth' });\n                }}\n              >\n                Skip to Partnership\n              </Button>\n            </div>\n          </motion.div>\n\n          {/* Property Plaza Stats */}\n          <HeroStats />\n\n          {/* Scroll Hint */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 1, delay: 3.0 }}\n            className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n          >\n            <motion.div\n              animate={{ y: [0, 10, 0] }}\n              transition={{ duration: 2, repeat: Infinity, ease: \"easeInOut\" }}\n              className=\"flex flex-col items-center space-y-2 text-text-muted\"\n            >\n              <span className=\"text-sm font-medium\">Scroll to explore</span>\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 14l-7 7m0 0l-7-7m7 7V3\" />\n              </svg>\n            </motion.div>\n          </motion.div>\n        </div>\n      </Section>\n\n      {/* 2. Market Problem */}\n      <Section id=\"problem\" className=\"bg-white\">\n        <div className=\"text-center space-y-12\">\n          <Heading level={2}>\n            The Market Problem\n          </Heading>\n\n          <div className=\"grid md:grid-cols-3 gap-8 mt-16\">\n            <PillarCard\n              icon=\"❓\"\n              title=\"Buyer Confusion\"\n              description=\"Confused by leasehold vs ownership rules and complex legal structures\"\n              delay={0.1}\n            />\n            <PillarCard\n              icon=\"🚫\"\n              title=\"Limited Access\"\n              description=\"Sellers lack access to serious foreign buyers and qualified leads\"\n              delay={0.2}\n            />\n            <PillarCard\n              icon=\"⚠️\"\n              title=\"Trust Issues\"\n              description=\"Low trust in the real estate space due to lack of transparency\"\n              delay={0.3}\n            />\n          </div>\n        </div>\n      </Section>\n\n      {/* 3. Mission & 4 Pillars - Placeholder */}\n      <Section id=\"mission\" className=\"bg-background\">\n        <div className=\"text-center\">\n          <Heading level={2}>Mission & 4 Pillars</Heading>\n          <Paragraph className=\"mt-4\">Coming Soon - Interactive pillar cards</Paragraph>\n        </div>\n      </Section>\n\n      {/* 4. About Property Plaza - Placeholder */}\n      <Section id=\"about\" className=\"bg-white\">\n        <div className=\"text-center\">\n          <Heading level={2}>About Property Plaza</Heading>\n          <Paragraph className=\"mt-4\">Coming Soon - Statistics and platform showcase</Paragraph>\n        </div>\n      </Section>\n\n      {/* 5. Paradise Indonesia Partnership - Placeholder */}\n      <Section id=\"partnership\" className=\"bg-background\">\n        <div className=\"text-center\">\n          <Heading level={2}>Paradise Indonesia Partnership</Heading>\n          <Paragraph className=\"mt-4\">Coming Soon - Why this collaboration</Paragraph>\n        </div>\n      </Section>\n\n      {/* 6. Synergy & Joint Value - Placeholder */}\n      <Section id=\"synergy\" className=\"bg-white\">\n        <div className=\"text-center\">\n          <Heading level={2}>Synergy & Joint Value</Heading>\n          <Paragraph className=\"mt-4\">Coming Soon - Logo merge animation</Paragraph>\n        </div>\n      </Section>\n\n      {/* 7. Pilot Campaign Plan - Placeholder */}\n      <Section id=\"pilot\" className=\"bg-background\">\n        <div className=\"text-center\">\n          <Heading level={2}>Pilot Campaign Plan</Heading>\n          <Paragraph className=\"mt-4\">Coming Soon - 4-week strategy details</Paragraph>\n        </div>\n      </Section>\n\n      {/* 8. Metrics & KPIs - Placeholder */}\n      <Section id=\"metrics\" className=\"bg-white\">\n        <div className=\"text-center\">\n          <Heading level={2}>Metrics & KPIs</Heading>\n          <Paragraph className=\"mt-4\">Coming Soon - Tracking dashboard preview</Paragraph>\n        </div>\n      </Section>\n\n      {/* 9. Final CTA - Placeholder */}\n      <Section id=\"cta\" className=\"bg-gradient-to-br from-primary/10 to-accent/10\">\n        <div className=\"text-center\">\n          <Heading level={2}>Let's Launch the Pilot</Heading>\n          <Paragraph className=\"mt-4\">Coming Soon - Call to action and contact</Paragraph>\n        </div>\n      </Section>\n    </ScrollContainer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAbA;;;;;;;;;;;;;AAee,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,cAAc;IAEpB,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,MAAM,iBAAiB,OAAO,OAAO;YACrC,MAAM,eAAe,OAAO,WAAW;YACvC,MAAM,WAAW,KAAK,KAAK,CAAC,iBAAiB,gBAAgB;YAC7D,gBAAgB,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,UAAU,IAAI;QAClD;QAEA,gDAAgD;QAChD,IAAI,UAAU;QACd,MAAM,wBAAwB;YAC5B,IAAI,CAAC,SAAS;gBACZ,sBAAsB;oBACpB;oBACA,UAAU;gBACZ;gBACA,UAAU;YACZ;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU,uBAAuB;YAAE,SAAS;QAAK;QACzE,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;IAClB;IAEA,qBACE,8OAAC,2IAAA,CAAA,UAAe;;0BAEd,8OAAC,sIAAA,CAAA,UAAU;gBACT,cAAc;gBACd,aAAa;gBACb,eAAe;;;;;;0BAIjB,8OAAC,0IAAA,CAAA,UAAc;gBAAC,cAAc;gBAAc,aAAa;;;;;;0BAGzD,8OAAC,2IAAA,CAAA,UAAO;gBAAC,IAAG;gBAAO,WAAU;;kCAC3B,8OAAC,uJAAA,CAAA,mBAAgB;;;;;kCAGjB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAG,OAAO;gCAAI;gCACtC,WAAU;0CAEV,cAAA,8OAAC,gIAAA,CAAA,eAAY;oCAAC,MAAK;;;;;;;;;;;0CAIrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;;kDAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAG,OAAO;wCAAI;kDAEtC,cAAA,8OAAC,sIAAA,CAAA,UAAO;4CAAC,OAAO;4CAAG,SAAS;4CAAO,QAAQ;4CAAC,WAAU;sDAAgB;;;;;;;;;;;kDAKxE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAG,OAAO;wCAAI;kDAEtC,cAAA,8OAAC,sIAAA,CAAA,UAAO;4CAAC,OAAO;4CAAG,SAAS;4CAAO,WAAU;sDAA0B;;;;;;;;;;;;;;;;;0CAO3E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAG,OAAO;gCAAI;gCACtC,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;8CACZ;wCACC;4CAAE,MAAM;4CAAM,MAAM;wCAAe;wCACnC;4CAAE,MAAM;4CAAM,MAAM;wCAAY;wCAChC;4CAAE,MAAM;4CAAM,MAAM;wCAAa;wCACjC;4CAAE,MAAM;4CAAM,MAAM;wCAAc;qCACnC,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,WAAU;4CACV,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAK,OAAO,MAAM,QAAQ;4CAAI;4CACtD,YAAY;gDACV,GAAG,CAAC;gDACJ,YAAY;oDAAE,MAAM;oDAAU,WAAW;oDAAK,SAAS;gDAAG;4CAC5D;;8DAEA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,YAAY;wDAAE,OAAO;wDAAK,QAAQ;oDAAE;oDACpC,YAAY;wDAAE,MAAM;wDAAU,WAAW;wDAAK,SAAS;oDAAG;8DAEzD,OAAO,IAAI;;;;;;8DAEd,8OAAC;oDAAK,WAAU;8DACb,OAAO,IAAI;;;;;;;2CAlBT,OAAO,IAAI;;;;;;;;;;;;;;;0CA0BxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAG,OAAO;gCAAI;gCACtC,WAAU;;kDAEV,8OAAC,sIAAA,CAAA,YAAS;wCAAC,SAAS;wCAAO,WAAU;kDAAuD;;;;;;kDAK5F,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,UAAM;gDACL,MAAK;gDACL,WAAU;gDACV,SAAS;oDACP,MAAM,iBAAiB,SAAS,cAAc,CAAC;oDAC/C,gBAAgB,eAAe;wDAAE,UAAU;oDAAS;gDACtD;0DACD;;;;;;0DAID,8OAAC,kIAAA,CAAA,UAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS;oDACP,MAAM,aAAa,SAAS,cAAc,CAAC;oDAC3C,YAAY,eAAe;wDAAE,UAAU;oDAAS;gDAClD;0DACD;;;;;;;;;;;;;;;;;;0CAOL,8OAAC,2IAAA,CAAA,UAAS;;;;;0CAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,UAAU;oCAAG,OAAO;gCAAI;gCACtC,WAAU;0CAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,GAAG;4CAAC;4CAAG;4CAAI;yCAAE;oCAAC;oCACzB,YAAY;wCAAE,UAAU;wCAAG,QAAQ;wCAAU,MAAM;oCAAY;oCAC/D,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;sDACtC,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ/E,8OAAC,2IAAA,CAAA,UAAO;gBAAC,IAAG;gBAAU,WAAU;0BAC9B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sIAAA,CAAA,UAAO;4BAAC,OAAO;sCAAG;;;;;;sCAInB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,aAAU;oCACT,MAAK;oCACL,OAAM;oCACN,aAAY;oCACZ,OAAO;;;;;;8CAET,8OAAC,gIAAA,CAAA,aAAU;oCACT,MAAK;oCACL,OAAM;oCACN,aAAY;oCACZ,OAAO;;;;;;8CAET,8OAAC,gIAAA,CAAA,aAAU;oCACT,MAAK;oCACL,OAAM;oCACN,aAAY;oCACZ,OAAO;;;;;;;;;;;;;;;;;;;;;;;0BAOf,8OAAC,2IAAA,CAAA,UAAO;gBAAC,IAAG;gBAAU,WAAU;0BAC9B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sIAAA,CAAA,UAAO;4BAAC,OAAO;sCAAG;;;;;;sCACnB,8OAAC,sIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAO;;;;;;;;;;;;;;;;;0BAKhC,8OAAC,2IAAA,CAAA,UAAO;gBAAC,IAAG;gBAAQ,WAAU;0BAC5B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sIAAA,CAAA,UAAO;4BAAC,OAAO;sCAAG;;;;;;sCACnB,8OAAC,sIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAO;;;;;;;;;;;;;;;;;0BAKhC,8OAAC,2IAAA,CAAA,UAAO;gBAAC,IAAG;gBAAc,WAAU;0BAClC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sIAAA,CAAA,UAAO;4BAAC,OAAO;sCAAG;;;;;;sCACnB,8OAAC,sIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAO;;;;;;;;;;;;;;;;;0BAKhC,8OAAC,2IAAA,CAAA,UAAO;gBAAC,IAAG;gBAAU,WAAU;0BAC9B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sIAAA,CAAA,UAAO;4BAAC,OAAO;sCAAG;;;;;;sCACnB,8OAAC,sIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAO;;;;;;;;;;;;;;;;;0BAKhC,8OAAC,2IAAA,CAAA,UAAO;gBAAC,IAAG;gBAAQ,WAAU;0BAC5B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sIAAA,CAAA,UAAO;4BAAC,OAAO;sCAAG;;;;;;sCACnB,8OAAC,sIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAO;;;;;;;;;;;;;;;;;0BAKhC,8OAAC,2IAAA,CAAA,UAAO;gBAAC,IAAG;gBAAU,WAAU;0BAC9B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sIAAA,CAAA,UAAO;4BAAC,OAAO;sCAAG;;;;;;sCACnB,8OAAC,sIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAO;;;;;;;;;;;;;;;;;0BAKhC,8OAAC,2IAAA,CAAA,UAAO;gBAAC,IAAG;gBAAM,WAAU;0BAC1B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sIAAA,CAAA,UAAO;4BAAC,OAAO;sCAAG;;;;;;sCACnB,8OAAC,sIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAO;;;;;;;;;;;;;;;;;;;;;;;AAKtC", "debugId": null}}]}